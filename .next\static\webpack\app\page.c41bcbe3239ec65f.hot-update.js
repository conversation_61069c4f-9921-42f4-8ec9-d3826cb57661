"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PregnancyCharts.tsx":
/*!********************************************!*\
  !*** ./src/components/PregnancyCharts.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PregnancyCharts; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Calendar_TrendingUp_Weight_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Calendar,TrendingUp,Weight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/baby.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Calendar_TrendingUp_Weight_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Calendar,TrendingUp,Weight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/weight.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Calendar_TrendingUp_Weight_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Calendar,TrendingUp,Weight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Calendar_TrendingUp_Weight_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Calendar,TrendingUp,Weight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PregnancyCharts(param) {\n    let { results } = param;\n    // بيانات نمو الجنين المحسنة بالذكاء الاصطناعي (معايير WHO وACOG)\n    const fetalGrowthData = [\n        {\n            week: 4,\n            length: 0.4,\n            weight: 0.02,\n            milestone: \"تكوين الأنبوب العصبي وبداية القلب\",\n            size: \"بذرة الخشخاش\",\n            percentile: {\n                p10: 0.01,\n                p50: 0.02,\n                p90: 0.03\n            },\n            development: \"تكوين الطبقات الجنينية الثلاث\"\n        },\n        {\n            week: 5,\n            length: 0.5,\n            weight: 0.03,\n            milestone: \"تطور الحبل الشوكي\",\n            size: \"بذرة السمسم\",\n            percentile: {\n                p10: 0.02,\n                p50: 0.03,\n                p90: 0.04\n            },\n            development: \"بداية تكوين الدماغ\"\n        },\n        {\n            week: 6,\n            length: 0.6,\n            weight: 0.04,\n            milestone: \"أول نبضات القلب (110-160 نبضة/دقيقة)\",\n            size: \"حبة العدس\",\n            percentile: {\n                p10: 0.03,\n                p50: 0.04,\n                p90: 0.05\n            },\n            development: \"تكوين الأطراف البدائية\"\n        },\n        {\n            week: 7,\n            length: 1.0,\n            weight: 0.8,\n            milestone: \"تطور الوجه والعينين\",\n            size: \"حبة التوت الأزرق\",\n            percentile: {\n                p10: 0.6,\n                p50: 0.8,\n                p90: 1.0\n            },\n            development: \"تكوين الكلى والكبد\"\n        },\n        {\n            week: 8,\n            length: 1.6,\n            weight: 1.0,\n            milestone: \"تكوين الأصابع والأطراف\",\n            size: \"حبة التوت\",\n            percentile: {\n                p10: 0.8,\n                p50: 1.0,\n                p90: 1.2\n            },\n            development: \"بداية تكوين الأعضاء التناسلية\"\n        },\n        {\n            week: 9,\n            length: 2.3,\n            weight: 2.0,\n            milestone: \"تطور العضلات والأعصاب\",\n            size: \"حبة العنب\",\n            percentile: {\n                p10: 1.5,\n                p50: 2.0,\n                p90: 2.5\n            },\n            development: \"تكوين الجفون\"\n        },\n        {\n            week: 10,\n            length: 3.1,\n            weight: 4.0,\n            milestone: \"تكوين الأظافر والشعر\",\n            size: \"حبة الفراولة\",\n            percentile: {\n                p10: 3.0,\n                p50: 4.0,\n                p90: 5.0\n            },\n            development: \"تطور الجهاز الهضمي\"\n        },\n        {\n            week: 11,\n            length: 4.1,\n            weight: 7.0,\n            milestone: \"تطور الأسنان والعظام\",\n            size: \"حبة التين\",\n            percentile: {\n                p10: 5.5,\n                p50: 7.0,\n                p90: 8.5\n            },\n            development: \"بداية إنتاج البول\"\n        },\n        {\n            week: 12,\n            length: 5.4,\n            weight: 14.0,\n            milestone: \"اكتمال الأعضاء الأساسية\",\n            size: \"حبة الليمون\",\n            percentile: {\n                p10: 11.0,\n                p50: 14.0,\n                p90: 17.0\n            },\n            development: \"تطور ردود الأفعال\"\n        },\n        {\n            week: 13,\n            length: 7.4,\n            weight: 23.0,\n            milestone: \"تطور الحبال الصوتية\",\n            size: \"حبة الخوخ الصغيرة\",\n            percentile: {\n                p10: 18.0,\n                p50: 23.0,\n                p90: 28.0\n            },\n            development: \"بداية تكوين بصمات الأصابع\"\n        },\n        {\n            week: 14,\n            length: 8.7,\n            weight: 43.0,\n            milestone: \"نمو الشعر والحواجب\",\n            size: \"حبة الخوخ\",\n            percentile: {\n                p10: 35.0,\n                p50: 43.0,\n                p90: 51.0\n            },\n            development: \"تطور الغدد اللعابية\"\n        },\n        {\n            week: 15,\n            length: 10.1,\n            weight: 70.0,\n            milestone: \"تطور حاسة التذوق\",\n            size: \"تفاحة صغيرة\",\n            percentile: {\n                p10: 55.0,\n                p50: 70.0,\n                p90: 85.0\n            },\n            development: \"تكوين الدهون البنية\"\n        },\n        {\n            week: 16,\n            length: 11.6,\n            weight: 100.0,\n            milestone: \"إمكانية تحديد الجنس\",\n            size: \"حبة الأفوكادو\",\n            percentile: {\n                p10: 80.0,\n                p50: 100.0,\n                p90: 120.0\n            },\n            development: \"تطور الجهاز العصبي المركزي\"\n        },\n        {\n            week: 17,\n            length: 13.0,\n            weight: 140.0,\n            milestone: \"تطور الدهون والعضلات\",\n            size: \"حبة الكمثرى\",\n            percentile: {\n                p10: 110.0,\n                p50: 140.0,\n                p90: 170.0\n            },\n            development: \"تكوين الميلانين\"\n        },\n        {\n            week: 18,\n            length: 14.2,\n            weight: 190.0,\n            milestone: \"تطور السمع وردود الأفعال\",\n            size: \"فلفل حلو\",\n            percentile: {\n                p10: 150.0,\n                p50: 190.0,\n                p90: 230.0\n            },\n            development: \"تطور الجهاز المناعي\"\n        },\n        {\n            week: 19,\n            length: 15.3,\n            weight: 240.0,\n            milestone: \"تطور الحواس الخمس\",\n            size: \"طماطم كبيرة\",\n            percentile: {\n                p10: 200.0,\n                p50: 240.0,\n                p90: 280.0\n            },\n            development: \"تكوين الطبقة الواقية للجلد\"\n        },\n        {\n            week: 20,\n            length: 16.4,\n            weight: 300.0,\n            milestone: \"منتصف الحمل - حركة واضحة\",\n            size: \"موزة\",\n            percentile: {\n                p10: 250.0,\n                p50: 300.0,\n                p90: 350.0\n            },\n            development: \"تطور أنماط النوم\"\n        },\n        {\n            week: 21,\n            length: 26.7,\n            weight: 360.0,\n            milestone: \"تطور الجهاز الهضمي\",\n            size: \"جزرة كبيرة\",\n            percentile: {\n                p10: 300.0,\n                p50: 360.0,\n                p90: 420.0\n            },\n            development: \"بداية هضم السائل الأمنيوسي\"\n        },\n        {\n            week: 22,\n            length: 27.8,\n            weight: 430.0,\n            milestone: \"تطور الحواس والذاكرة\",\n            size: \"بابايا صغيرة\",\n            percentile: {\n                p10: 350.0,\n                p50: 430.0,\n                p90: 510.0\n            },\n            development: \"تكوين خلايا الدماغ\"\n        },\n        {\n            week: 23,\n            length: 28.9,\n            weight: 501.0,\n            milestone: \"تطور الرئتين\",\n            size: \"مانجو\",\n            percentile: {\n                p10: 420.0,\n                p50: 501.0,\n                p90: 582.0\n            },\n            development: \"إنتاج السائل السطحي\"\n        },\n        {\n            week: 24,\n            length: 30.0,\n            weight: 600.0,\n            milestone: \"بداية القدرة على البقاء\",\n            size: \"كوز ذرة\",\n            percentile: {\n                p10: 500.0,\n                p50: 600.0,\n                p90: 700.0\n            },\n            development: \"تطور الأوعية الدموية في الرئتين\"\n        },\n        {\n            week: 25,\n            length: 34.6,\n            weight: 660.0,\n            milestone: \"تطور الشعر والأظافر\",\n            size: \"قرنبيط\",\n            percentile: {\n                p10: 550.0,\n                p50: 660.0,\n                p90: 770.0\n            },\n            development: \"تطور ردود الأفعال للضوء\"\n        },\n        {\n            week: 26,\n            length: 35.6,\n            weight: 760.0,\n            milestone: \"فتح العينين\",\n            size: \"خس\",\n            percentile: {\n                p10: 630.0,\n                p50: 760.0,\n                p90: 890.0\n            },\n            development: \"تطور الشبكية\"\n        },\n        {\n            week: 27,\n            length: 36.6,\n            weight: 875.0,\n            milestone: \"تطور الدماغ السريع\",\n            size: \"قرنبيط كبير\",\n            percentile: {\n                p10: 720.0,\n                p50: 875.0,\n                p90: 1030.0\n            },\n            development: \"تكوين الأخاديد في الدماغ\"\n        },\n        {\n            week: 28,\n            length: 37.6,\n            weight: 1005.0,\n            milestone: \"تطور الجهاز العصبي\",\n            size: \"باذنجان\",\n            percentile: {\n                p10: 820.0,\n                p50: 1005.0,\n                p90: 1190.0\n            },\n            development: \"تطور دورات النوم والاستيقاظ\"\n        },\n        {\n            week: 29,\n            length: 38.6,\n            weight: 1153.0,\n            milestone: \"تطور العضلات والعظام\",\n            size: \"اسكواش\",\n            percentile: {\n                p10: 940.0,\n                p50: 1153.0,\n                p90: 1366.0\n            },\n            development: \"تطور نخاع العظام\"\n        },\n        {\n            week: 30,\n            length: 39.9,\n            weight: 1319.0,\n            milestone: \"نمو الدماغ والرئتين\",\n            size: \"كرنب\",\n            percentile: {\n                p10: 1080.0,\n                p50: 1319.0,\n                p90: 1558.0\n            },\n            development: \"تطور التحكم في درجة الحرارة\"\n        },\n        {\n            week: 31,\n            length: 41.1,\n            weight: 1502.0,\n            milestone: \"تطور الجهاز المناعي\",\n            size: \"جوز هند\",\n            percentile: {\n                p10: 1235.0,\n                p50: 1502.0,\n                p90: 1769.0\n            },\n            development: \"إنتاج خلايا الدم الحمراء\"\n        },\n        {\n            week: 32,\n            length: 42.4,\n            weight: 1702.0,\n            milestone: \"تطور الأظافر والشعر\",\n            size: \"كالي\",\n            percentile: {\n                p10: 1405.0,\n                p50: 1702.0,\n                p90: 1999.0\n            },\n            development: \"تطور طبقات الجلد\"\n        },\n        {\n            week: 33,\n            length: 43.7,\n            weight: 1918.0,\n            milestone: \"تطور الجهاز التنفسي\",\n            size: \"أناناس\",\n            percentile: {\n                p10: 1590.0,\n                p50: 1918.0,\n                p90: 2246.0\n            },\n            development: \"نضج الرئتين\"\n        },\n        {\n            week: 34,\n            length: 45.0,\n            weight: 2146.0,\n            milestone: \"اكتمال الرئتين تقريباً\",\n            size: \"شمام صغير\",\n            percentile: {\n                p10: 1785.0,\n                p50: 2146.0,\n                p90: 2507.0\n            },\n            development: \"تطور الجهاز الهضمي\"\n        },\n        {\n            week: 35,\n            length: 46.2,\n            weight: 2383.0,\n            milestone: \"تطور الكلى والكبد\",\n            size: \"شمام عسلي\",\n            percentile: {\n                p10: 1995.0,\n                p50: 2383.0,\n                p90: 2771.0\n            },\n            development: \"تطور وظائف الكبد\"\n        },\n        {\n            week: 36,\n            length: 47.4,\n            weight: 2622.0,\n            milestone: \"اكتمال معظم الأعضاء\",\n            size: \"خس روماني\",\n            percentile: {\n                p10: 2212.0,\n                p50: 2622.0,\n                p90: 3032.0\n            },\n            development: \"تطور الجهاز الهضمي الكامل\"\n        },\n        {\n            week: 37,\n            length: 48.6,\n            weight: 2859.0,\n            milestone: \"الحمل مكتمل المدة\",\n            size: \"سلق سويسري\",\n            percentile: {\n                p10: 2430.0,\n                p50: 2859.0,\n                p90: 3288.0\n            },\n            development: \"تطور المناعة الطبيعية\"\n        },\n        {\n            week: 38,\n            length: 49.8,\n            weight: 3083.0,\n            milestone: \"الاستعداد للولادة\",\n            size: \"كراث\",\n            percentile: {\n                p10: 2640.0,\n                p50: 3083.0,\n                p90: 3526.0\n            },\n            development: \"تطور ردود الأفعال للولادة\"\n        },\n        {\n            week: 39,\n            length: 50.7,\n            weight: 3288.0,\n            milestone: \"نضج كامل للأعضاء\",\n            size: \"بطيخ صغير\",\n            percentile: {\n                p10: 2835.0,\n                p50: 3288.0,\n                p90: 3741.0\n            },\n            development: \"تطور الجهاز العصبي الكامل\"\n        },\n        {\n            week: 40,\n            length: 51.2,\n            weight: 3462.0,\n            milestone: \"مكتمل النمو وجاهز للولادة\",\n            size: \"بطيخ\",\n            percentile: {\n                p10: 3008.0,\n                p50: 3462.0,\n                p90: 3916.0\n            },\n            development: \"جاهز للحياة خارج الرحم\"\n        },\n        {\n            week: 41,\n            length: 51.7,\n            weight: 3597.0,\n            milestone: \"نمو إضافي\",\n            size: \"يقطين صغير\",\n            percentile: {\n                p10: 3150.0,\n                p50: 3597.0,\n                p90: 4044.0\n            },\n            development: \"نضج إضافي للرئتين\"\n        },\n        {\n            week: 42,\n            length: 52.0,\n            weight: 3685.0,\n            milestone: \"تأخر الولادة\",\n            size: \"يقطين\",\n            percentile: {\n                p10: 3250.0,\n                p50: 3685.0,\n                p90: 4120.0\n            },\n            development: \"مراقبة طبية مكثفة مطلوبة\"\n        }\n    ];\n    // بيانات توزيع الثلثات\n    const trimesterData = [\n        {\n            name: \"الثلث الأول\",\n            value: 12,\n            color: \"#ec4899\",\n            weeks: \"1-12\"\n        },\n        {\n            name: \"الثلث الثاني\",\n            value: 14,\n            color: \"#8b5cf6\",\n            weeks: \"13-26\"\n        },\n        {\n            name: \"الثلث الثالث\",\n            value: 14,\n            color: \"#06b6d4\",\n            weeks: \"27-40\"\n        }\n    ];\n    // بيانات التطور الأسبوعي\n    const weeklyProgressData = Array.from({\n        length: 40\n    }, (_, i)=>{\n        const week = i + 1;\n        const progress = week / 40 * 100;\n        return {\n            week,\n            progress,\n            current: week === results.currentWeeks\n        };\n    });\n    // بيانات الفحوصات المهمة مع تفاصيل أكثر\n    const importantCheckups = [\n        {\n            week: 6,\n            test: \"أول زيارة طبية\",\n            importance: \"عالية\",\n            description: \"تأكيد الحمل وفحص عام\"\n        },\n        {\n            week: 8,\n            test: \"فحص الموجات فوق الصوتية الأول\",\n            importance: \"عالية\",\n            description: \"تحديد عمر الحمل ونبضات القلب\"\n        },\n        {\n            week: 11,\n            test: \"فحص الشفافية القفوية\",\n            importance: \"عالية\",\n            description: \"فحص التشوهات الخلقية\"\n        },\n        {\n            week: 16,\n            test: \"فحص الدم الثلاثي\",\n            importance: \"متوسطة\",\n            description: \"فحص متلازمة داون وعيوب الأنبوب العصبي\"\n        },\n        {\n            week: 20,\n            test: \"الموجات فوق الصوتية المفصلة\",\n            importance: \"عالية\",\n            description: \"فحص شامل لنمو الجنين وتحديد الجنس\"\n        },\n        {\n            week: 24,\n            test: \"فحص سكري الحمل\",\n            importance: \"عالية\",\n            description: \"اختبار تحمل الجلوكوز\"\n        },\n        {\n            week: 28,\n            test: \"فحص الأجسام المضادة\",\n            importance: \"متوسطة\",\n            description: \"فحص عامل الريسوس والأنيميا\"\n        },\n        {\n            week: 32,\n            test: \"مراقبة النمو والوضعية\",\n            importance: \"متوسطة\",\n            description: \"تقييم نمو الجنين ووضعيته\"\n        },\n        {\n            week: 36,\n            test: \"فحص البكتيريا العقدية\",\n            importance: \"عالية\",\n            description: \"فحص البكتيريا العقدية المجموعة ب\"\n        },\n        {\n            week: 38,\n            test: \"تقييم الاستعداد للولادة\",\n            importance: \"عالية\",\n            description: \"فحص عنق الرحم ووضعية الجنين\"\n        }\n    ];\n    const currentGrowthData = fetalGrowthData.find((data)=>data.week <= results.currentWeeks) || fetalGrowthData[0];\n    const nextMilestone = fetalGrowthData.find((data)=>data.week > results.currentWeeks);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card text-center relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-pink-200 to-red-200 rounded-full opacity-30 -translate-y-8 translate-x-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-pink-400 to-red-500 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Calendar_TrendingUp_Weight_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            className: \"w-7 h-7 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-bold text-gray-900 mb-2\",\n                                        children: \"طول الجنين\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-pink-600 mb-1\",\n                                        children: [\n                                            currentGrowthData.length,\n                                            \" سم\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: currentGrowthData.size\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card text-center relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-purple-200 to-indigo-200 rounded-full opacity-30 -translate-y-8 translate-x-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-purple-400 to-indigo-500 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Calendar_TrendingUp_Weight_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-7 h-7 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-bold text-gray-900 mb-2\",\n                                        children: \"وزن الجنين\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-purple-600 mb-1\",\n                                        children: [\n                                            currentGrowthData.weight,\n                                            \" جم\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"الوزن الطبيعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card text-center relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-blue-200 to-cyan-200 rounded-full opacity-30 -translate-y-8 translate-x-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-blue-400 to-cyan-500 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Calendar_TrendingUp_Weight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-7 h-7 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-bold text-gray-900 mb-2\",\n                                        children: \"الأسبوع الحالي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-blue-600 mb-1\",\n                                        children: results.currentWeeks\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"أسبوع من الحمل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card text-center relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-green-200 to-emerald-200 rounded-full opacity-30 -translate-y-8 translate-x-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-green-400 to-emerald-500 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Calendar_TrendingUp_Weight_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-7 h-7 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-bold text-gray-900 mb-2\",\n                                        children: \"الثلث الحالي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-green-600 mb-1\",\n                                        children: results.trimester\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"من أثلاث الحمل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-gradient text-white relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: \"التطور الحالي للجنين\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl mb-4\",\n                                children: currentGrowthData.milestone\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/20 backdrop-blur-sm rounded-xl p-4 inline-block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: [\n                                        \"حجم الجنين: \",\n                                        currentGrowthData.size\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-20 h-20 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full opacity-50 -translate-y-10 -translate-x-10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-xl bg-gradient-to-br from-pink-400 to-purple-500 flex items-center justify-center ml-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Calendar_TrendingUp_Weight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"منحنى نمو الجنين\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-pink-500 rounded-full ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"الطول (سم)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-purple-500 rounded-full ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"الوزن (جم)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-96 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.LineChart, {\n                                        data: fetalGrowthData,\n                                        margin: {\n                                            top: 20,\n                                            right: 30,\n                                            left: 20,\n                                            bottom: 60\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\",\n                                                stroke: \"#e5e7eb\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.XAxis, {\n                                                dataKey: \"week\",\n                                                stroke: \"#6b7280\",\n                                                fontSize: 12,\n                                                label: {\n                                                    value: \"أسبوع الحمل\",\n                                                    position: \"insideBottom\",\n                                                    offset: -10,\n                                                    style: {\n                                                        textAnchor: \"middle\",\n                                                        fill: \"#6b7280\"\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.YAxis, {\n                                                yAxisId: \"length\",\n                                                orientation: \"right\",\n                                                stroke: \"#ec4899\",\n                                                fontSize: 12,\n                                                label: {\n                                                    value: \"الطول (سم)\",\n                                                    angle: 90,\n                                                    position: \"insideRight\",\n                                                    style: {\n                                                        textAnchor: \"middle\",\n                                                        fill: \"#ec4899\"\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.YAxis, {\n                                                yAxisId: \"weight\",\n                                                orientation: \"left\",\n                                                stroke: \"#8b5cf6\",\n                                                fontSize: 12,\n                                                label: {\n                                                    value: \"الوزن (جم)\",\n                                                    angle: 90,\n                                                    position: \"insideLeft\",\n                                                    style: {\n                                                        textAnchor: \"middle\",\n                                                        fill: \"#8b5cf6\"\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                                contentStyle: {\n                                                    backgroundColor: \"rgba(255, 255, 255, 0.95)\",\n                                                    border: \"none\",\n                                                    borderRadius: \"12px\",\n                                                    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.1)\",\n                                                    fontSize: \"14px\"\n                                                },\n                                                labelFormatter: (value)=>\"الأسبوع \".concat(value),\n                                                formatter: (value, name, props)=>[\n                                                        \"\".concat(value, \" \").concat(name === \"length\" ? \"سم\" : \"جم\"),\n                                                        name === \"length\" ? \"الطول\" : \"الوزن\"\n                                                    ]\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Line, {\n                                                yAxisId: \"length\",\n                                                type: \"monotone\",\n                                                dataKey: \"length\",\n                                                stroke: \"#ec4899\",\n                                                strokeWidth: 4,\n                                                dot: {\n                                                    fill: \"#ec4899\",\n                                                    strokeWidth: 2,\n                                                    r: 6\n                                                },\n                                                activeDot: {\n                                                    r: 8,\n                                                    fill: \"#ec4899\",\n                                                    stroke: \"#fff\",\n                                                    strokeWidth: 2\n                                                },\n                                                name: \"length\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Line, {\n                                                yAxisId: \"weight\",\n                                                type: \"monotone\",\n                                                dataKey: \"weight\",\n                                                stroke: \"#8b5cf6\",\n                                                strokeWidth: 4,\n                                                dot: {\n                                                    fill: \"#8b5cf6\",\n                                                    strokeWidth: 2,\n                                                    r: 6\n                                                },\n                                                activeDot: {\n                                                    r: 8,\n                                                    fill: \"#8b5cf6\",\n                                                    stroke: \"#fff\",\n                                                    strokeWidth: 2\n                                                },\n                                                name: \"weight\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl border border-pink-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 bg-pink-500 rounded-full ml-2 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-800\",\n                                            children: [\n                                                \"أنت الآن في الأسبوع \",\n                                                results.currentWeeks,\n                                                \" - الطول: \",\n                                                currentGrowthData.length,\n                                                \" سم، الوزن: \",\n                                                currentGrowthData.weight,\n                                                \" جم\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"توزيع أثلاث الحمل\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.PieChart, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Pie, {\n                                                data: trimesterData,\n                                                cx: \"50%\",\n                                                cy: \"50%\",\n                                                innerRadius: 60,\n                                                outerRadius: 100,\n                                                paddingAngle: 5,\n                                                dataKey: \"value\",\n                                                children: trimesterData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Cell, {\n                                                        fill: entry.color\n                                                    }, \"cell-\".concat(index), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                                formatter: (value, name, props)=>[\n                                                        \"\".concat(value, \" أسبوع\"),\n                                                        props.payload.weeks\n                                                    ]\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center space-x-4 space-x-reverse mt-4\",\n                                children: trimesterData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full ml-2\",\n                                                style: {\n                                                    backgroundColor: item.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"الفحوصات المهمة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.BarChart, {\n                                        data: importantCheckups,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.XAxis, {\n                                                dataKey: \"week\",\n                                                label: {\n                                                    value: \"الأسبوع\",\n                                                    position: \"insideBottom\",\n                                                    offset: -5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.YAxis, {\n                                                hide: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                                labelFormatter: (value)=>\"الأسبوع \".concat(value),\n                                                formatter: (value, name, props)=>[\n                                                        props.payload.test,\n                                                        \"أهمية \".concat(props.payload.importance)\n                                                    ]\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.Bar, {\n                                                dataKey: \"week\",\n                                                fill: (entry)=>{\n                                                    var _importantCheckups_find;\n                                                    const importance = (_importantCheckups_find = importantCheckups.find((item)=>item.week === entry)) === null || _importantCheckups_find === void 0 ? void 0 : _importantCheckups_find.importance;\n                                                    return importance === \"عالية\" ? \"#ef4444\" : importance === \"متوسطة\" ? \"#f59e0b\" : \"#10b981\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            nextMilestone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card bg-gradient-to-r from-pink-50 to-purple-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"المعلم التالي\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700\",\n                                    children: [\n                                        \"الأسبوع \",\n                                        nextMilestone.week,\n                                        \": \",\n                                        nextMilestone.milestone\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: [\n                                        \"متبقي \",\n                                        nextMilestone.week - results.currentWeeks,\n                                        \" أسبوع\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Calendar_TrendingUp_Weight_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-12 h-12 text-pink-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n                lineNumber: 334,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyCharts.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_c = PregnancyCharts;\nvar _c;\n$RefreshReg$(_c, \"PregnancyCharts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PregnancyCharts.tsx\n"));

/***/ })

});