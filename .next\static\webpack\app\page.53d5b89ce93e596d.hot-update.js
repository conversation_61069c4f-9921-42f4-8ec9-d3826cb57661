"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/HealthAnalytics.tsx":
/*!********************************************!*\
  !*** ./src/components/HealthAnalytics.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HealthAnalytics; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction HealthAnalytics(param) {\n    let { results, lastMenstrualPeriod } = param;\n    _s();\n    const [insights, setInsights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [riskAssessment, setRiskAssessment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        analyzeHealthData();\n    }, [\n        results,\n        lastMenstrualPeriod\n    ]);\n    const analyzeHealthData = async ()=>{\n        setIsAnalyzing(true);\n        // محاكاة تحليل البيانات\n        setTimeout(()=>{\n            const generatedInsights = generateHealthInsights();\n            const assessment = generateRiskAssessment();\n            setInsights(generatedInsights);\n            setRiskAssessment(assessment);\n            setIsAnalyzing(false);\n        }, 2000);\n    };\n    const generateHealthInsights = ()=>{\n        const insights = [];\n        const { currentWeeks, trimester, progressPercentage } = results;\n        // تحليل مرحلة الحمل\n        if (trimester === 1) {\n            insights.push({\n                id: \"trimester1-nutrition\",\n                type: \"positive\",\n                title: \"التغذية في الثلث الأول - مرحلة حاسمة\",\n                description: \"أنت في أهم مرحلة لتكوين الأعضاء الحيوية للجنين\",\n                recommendation: \"ركزي على حمض الفوليك (400-800 ميكروجرام)، الحديد، والكالسيوم. تجنبي الكافيين الزائد والأطعمة النيئة\",\n                confidence: 0.95,\n                category: \"nutrition\"\n            });\n            if (currentWeeks >= 6 && currentWeeks <= 12) {\n                insights.push({\n                    id: \"morning-sickness\",\n                    type: \"neutral\",\n                    title: \"إدارة أعراض الحمل المبكرة\",\n                    description: \"الغثيان والتعب شائعان في هذه المرحلة\",\n                    recommendation: \"تناولي وجبات صغيرة متكررة واشربي الزنجبيل\",\n                    confidence: 0.85,\n                    category: \"lifestyle\"\n                });\n            }\n        } else if (trimester === 2) {\n            insights.push({\n                id: \"trimester2-exercise\",\n                type: \"positive\",\n                title: \"الثلث الثاني - فترة الطاقة\",\n                description: \"هذه أفضل فترة لممارسة الرياضة والأنشطة\",\n                recommendation: \"ابدئي برنامج تمارين آمن للحمل\",\n                confidence: 0.88,\n                category: \"exercise\"\n            });\n            if (currentWeeks >= 18 && currentWeeks <= 22) {\n                insights.push({\n                    id: \"anatomy-scan\",\n                    type: \"warning\",\n                    title: \"فحص الموجات فوق الصوتية المفصل\",\n                    description: \"وقت مهم لفحص تطور الجنين\",\n                    recommendation: \"احجزي موعد فحص الموجات فوق الصوتية\",\n                    confidence: 0.95,\n                    category: \"medical\"\n                });\n            }\n        } else {\n            insights.push({\n                id: \"trimester3-preparation\",\n                type: \"warning\",\n                title: \"الاستعداد للولادة\",\n                description: \"الثلث الأخير - وقت التحضير\",\n                recommendation: \"ابدئي بحضور دورات الولادة وتحضير حقيبة المستشفى\",\n                confidence: 0.92,\n                category: \"lifestyle\"\n            });\n            if (currentWeeks >= 36) {\n                insights.push({\n                    id: \"full-term-prep\",\n                    type: \"critical\",\n                    title: \"اقتراب موعد الولادة\",\n                    description: \"الجنين مكتمل النمو تقريباً\",\n                    recommendation: \"كوني مستعدة للولادة في أي وقت\",\n                    confidence: 0.98,\n                    category: \"medical\"\n                });\n            }\n        }\n        // تحليل التقدم\n        if (progressPercentage > 50) {\n            insights.push({\n                id: \"progress-milestone\",\n                type: \"positive\",\n                title: \"تجاوزت منتصف الحمل\",\n                description: \"أكملت \".concat(progressPercentage.toFixed(0), \"% من رحلة الحمل\"),\n                recommendation: \"استمري في المتابعة الطبية المنتظمة\",\n                confidence: 0.9,\n                category: \"medical\"\n            });\n        }\n        return insights;\n    };\n    const generateRiskAssessment = ()=>{\n        const { currentWeeks, trimester } = results;\n        // تقييم المخاطر بناءً على البيانات المتاحة\n        let overall = \"low\";\n        const recommendations = [];\n        if (trimester === 1) {\n            recommendations.push(\"تناولي حمض الفوليك يومياً\");\n            recommendations.push(\"تجنبي الكحول والتدخين\");\n        } else if (trimester === 2) {\n            recommendations.push(\"مارسي الرياضة الخفيفة\");\n            recommendations.push(\"راقبي زيادة الوزن\");\n        } else {\n            recommendations.push(\"راقبي حركة الجنين يومياً\");\n            recommendations.push(\"استعدي للولادة\");\n            if (currentWeeks > 42) {\n                overall = \"medium\";\n                recommendations.push(\"استشيري الطبيب فوراً - تأخر الولادة\");\n            }\n        }\n        return {\n            overall,\n            factors: {\n                medical: \"low\",\n                lifestyle: \"low\"\n            },\n            recommendations\n        };\n    };\n    const getInsightIcon = (type)=>{\n        switch(type){\n            case \"positive\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 31\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 30\n                }, this);\n            case \"critical\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 31\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getInsightColor = (type)=>{\n        switch(type){\n            case \"positive\":\n                return \"bg-green-50 border-green-200\";\n            case \"warning\":\n                return \"bg-yellow-50 border-yellow-200\";\n            case \"critical\":\n                return \"bg-red-50 border-red-200\";\n            default:\n                return \"bg-blue-50 border-blue-200\";\n        }\n    };\n    const getRiskColor = (risk)=>{\n        switch(risk){\n            case \"high\":\n                return \"text-red-600 bg-red-100\";\n            case \"medium\":\n                return \"text-yellow-600 bg-yellow-100\";\n            default:\n                return \"text-green-600 bg-green-100\";\n        }\n    };\n    if (isAnalyzing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        animate: {\n                            rotate: 360\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"inline-block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-12 h-12 text-purple-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mt-4 mb-2\",\n                        children: \"تحليل البيانات الصحية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"الذكاء الاصطناعي يحلل بياناتك...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n            lineNumber: 206,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            riskAssessment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-6 h-6 ml-2 text-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this),\n                            \"تقييم المخاطر الذكي\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-3\",\n                                        children: \"التقييم العام\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-4 py-2 rounded-full \".concat(getRiskColor(riskAssessment.overall)),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: riskAssessment.overall === \"low\" ? \"مخاطر منخفضة\" : riskAssessment.overall === \"medium\" ? \"مخاطر متوسطة\" : \"مخاطر عالية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-3\",\n                                        children: \"التوصيات الذكية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-1 text-sm\",\n                                        children: riskAssessment.recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-3 h-3 text-green-500 ml-1 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    rec\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                lineNumber: 226,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-6 h-6 ml-2 text-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            \"الرؤى الصحية المخصصة\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: insights.map((insight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"border rounded-lg p-4 \".concat(getInsightColor(insight.type)),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3 space-x-reverse\",\n                                            children: [\n                                                getInsightIcon(insight.type),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold\",\n                                                            children: insight.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm mt-1 opacity-80\",\n                                                            children: insight.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 p-2 bg-white bg-opacity-50 rounded\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"\\uD83D\\uDCA1 التوصية:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: insight.recommendation\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"دقة \",\n                                                (insight.confidence * 100).toFixed(0),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            }, insight.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card bg-gradient-to-r from-purple-50 to-pink-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-8 h-8 text-purple-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-purple-900\",\n                                    children: \"تحليل ذكي مخصص\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-purple-700 mt-1\",\n                                    children: \"هذا التحليل مبني على الذكاء الاصطناعي وبياناتك الشخصية. استشيري طبيبك دائماً للحصول على نصائح طبية دقيقة.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\HealthAnalytics.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(HealthAnalytics, \"vOi1Ffoo9VhkwD+4I8Gmfyovg00=\");\n_c = HealthAnalytics;\nvar _c;\n$RefreshReg$(_c, \"HealthAnalytics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0hlYWx0aEFuYWx5dGljcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUM4QztBQUVuRDtBQTRCeEIsU0FBU1MsZ0JBQWdCLEtBQXNEO1FBQXRELEVBQUVDLE9BQU8sRUFBRUMsbUJBQW1CLEVBQXdCLEdBQXREOztJQUN0QyxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2IsK0NBQVFBLENBQWtCLEVBQUU7SUFDNUQsTUFBTSxDQUFDYyxnQkFBZ0JDLGtCQUFrQixHQUFHZiwrQ0FBUUEsQ0FBd0I7SUFDNUUsTUFBTSxDQUFDZ0IsYUFBYUMsZUFBZSxHQUFHakIsK0NBQVFBLENBQUM7SUFFL0NDLGdEQUFTQSxDQUFDO1FBQ1JpQjtJQUNGLEdBQUc7UUFBQ1I7UUFBU0M7S0FBb0I7SUFFakMsTUFBTU8sb0JBQW9CO1FBQ3hCRCxlQUFlO1FBRWYsd0JBQXdCO1FBQ3hCRSxXQUFXO1lBQ1QsTUFBTUMsb0JBQW9CQztZQUMxQixNQUFNQyxhQUFhQztZQUVuQlYsWUFBWU87WUFDWkwsa0JBQWtCTztZQUNsQkwsZUFBZTtRQUNqQixHQUFHO0lBQ0w7SUFFQSxNQUFNSSx5QkFBeUI7UUFDN0IsTUFBTVQsV0FBNEIsRUFBRTtRQUNwQyxNQUFNLEVBQUVZLFlBQVksRUFBRUMsU0FBUyxFQUFFQyxrQkFBa0IsRUFBRSxHQUFHaEI7UUFFeEQsb0JBQW9CO1FBQ3BCLElBQUllLGNBQWMsR0FBRztZQUNuQmIsU0FBU2UsSUFBSSxDQUFDO2dCQUNaQyxJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxnQkFBZ0I7Z0JBQ2hCQyxZQUFZO2dCQUNaQyxVQUFVO1lBQ1o7WUFFQSxJQUFJVixnQkFBZ0IsS0FBS0EsZ0JBQWdCLElBQUk7Z0JBQzNDWixTQUFTZSxJQUFJLENBQUM7b0JBQ1pDLElBQUk7b0JBQ0pDLE1BQU07b0JBQ05DLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLGdCQUFnQjtvQkFDaEJDLFlBQVk7b0JBQ1pDLFVBQVU7Z0JBQ1o7WUFDRjtRQUNGLE9BQU8sSUFBSVQsY0FBYyxHQUFHO1lBQzFCYixTQUFTZSxJQUFJLENBQUM7Z0JBQ1pDLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLGdCQUFnQjtnQkFDaEJDLFlBQVk7Z0JBQ1pDLFVBQVU7WUFDWjtZQUVBLElBQUlWLGdCQUFnQixNQUFNQSxnQkFBZ0IsSUFBSTtnQkFDNUNaLFNBQVNlLElBQUksQ0FBQztvQkFDWkMsSUFBSTtvQkFDSkMsTUFBTTtvQkFDTkMsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsZ0JBQWdCO29CQUNoQkMsWUFBWTtvQkFDWkMsVUFBVTtnQkFDWjtZQUNGO1FBQ0YsT0FBTztZQUNMdEIsU0FBU2UsSUFBSSxDQUFDO2dCQUNaQyxJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxnQkFBZ0I7Z0JBQ2hCQyxZQUFZO2dCQUNaQyxVQUFVO1lBQ1o7WUFFQSxJQUFJVixnQkFBZ0IsSUFBSTtnQkFDdEJaLFNBQVNlLElBQUksQ0FBQztvQkFDWkMsSUFBSTtvQkFDSkMsTUFBTTtvQkFDTkMsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsZ0JBQWdCO29CQUNoQkMsWUFBWTtvQkFDWkMsVUFBVTtnQkFDWjtZQUNGO1FBQ0Y7UUFFQSxlQUFlO1FBQ2YsSUFBSVIscUJBQXFCLElBQUk7WUFDM0JkLFNBQVNlLElBQUksQ0FBQztnQkFDWkMsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsYUFBYSxTQUF1QyxPQUE5QkwsbUJBQW1CUyxPQUFPLENBQUMsSUFBRztnQkFDcERILGdCQUFnQjtnQkFDaEJDLFlBQVk7Z0JBQ1pDLFVBQVU7WUFDWjtRQUNGO1FBRUEsT0FBT3RCO0lBQ1Q7SUFFQSxNQUFNVyx5QkFBeUI7UUFDN0IsTUFBTSxFQUFFQyxZQUFZLEVBQUVDLFNBQVMsRUFBRSxHQUFHZjtRQUVwQywyQ0FBMkM7UUFDM0MsSUFBSTBCLFVBQXFDO1FBQ3pDLE1BQU1DLGtCQUE0QixFQUFFO1FBRXBDLElBQUlaLGNBQWMsR0FBRztZQUNuQlksZ0JBQWdCVixJQUFJLENBQUM7WUFDckJVLGdCQUFnQlYsSUFBSSxDQUFDO1FBQ3ZCLE9BQU8sSUFBSUYsY0FBYyxHQUFHO1lBQzFCWSxnQkFBZ0JWLElBQUksQ0FBQztZQUNyQlUsZ0JBQWdCVixJQUFJLENBQUM7UUFDdkIsT0FBTztZQUNMVSxnQkFBZ0JWLElBQUksQ0FBQztZQUNyQlUsZ0JBQWdCVixJQUFJLENBQUM7WUFDckIsSUFBSUgsZUFBZSxJQUFJO2dCQUNyQlksVUFBVTtnQkFDVkMsZ0JBQWdCVixJQUFJLENBQUM7WUFDdkI7UUFDRjtRQUVBLE9BQU87WUFDTFM7WUFDQUUsU0FBUztnQkFDUEMsU0FBUztnQkFDVEMsV0FBVztZQUNiO1lBQ0FIO1FBQ0Y7SUFDRjtJQUVBLE1BQU1JLGlCQUFpQixDQUFDWjtRQUN0QixPQUFRQTtZQUNOLEtBQUs7Z0JBQVkscUJBQU8sOERBQUN4QixpSUFBV0E7b0JBQUNxQyxXQUFVOzs7Ozs7WUFDL0MsS0FBSztnQkFBVyxxQkFBTyw4REFBQ3RDLGlJQUFhQTtvQkFBQ3NDLFdBQVU7Ozs7OztZQUNoRCxLQUFLO2dCQUFZLHFCQUFPLDhEQUFDdEMsaUlBQWFBO29CQUFDc0MsV0FBVTs7Ozs7O1lBQ2pEO2dCQUFTLHFCQUFPLDhEQUFDcEMsaUlBQU1BO29CQUFDb0MsV0FBVTs7Ozs7O1FBQ3BDO0lBQ0Y7SUFFQSxNQUFNQyxrQkFBa0IsQ0FBQ2Q7UUFDdkIsT0FBUUE7WUFDTixLQUFLO2dCQUFZLE9BQU87WUFDeEIsS0FBSztnQkFBVyxPQUFPO1lBQ3ZCLEtBQUs7Z0JBQVksT0FBTztZQUN4QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNZSxlQUFlLENBQUNDO1FBQ3BCLE9BQVFBO1lBQ04sS0FBSztnQkFBUSxPQUFPO1lBQ3BCLEtBQUs7Z0JBQVUsT0FBTztZQUN0QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxJQUFJN0IsYUFBYTtRQUNmLHFCQUNFLDhEQUFDOEI7WUFBSUosV0FBVTtzQkFDYiw0RUFBQ0k7Z0JBQUlKLFdBQVU7O2tDQUNiLDhEQUFDbEMsaURBQU1BLENBQUNzQyxHQUFHO3dCQUNUQyxTQUFTOzRCQUFFQyxRQUFRO3dCQUFJO3dCQUN2QkMsWUFBWTs0QkFBRUMsVUFBVTs0QkFBR0MsUUFBUUM7NEJBQVVDLE1BQU07d0JBQVM7d0JBQzVEWCxXQUFVO2tDQUVWLDRFQUFDeEMsaUlBQUtBOzRCQUFDd0MsV0FBVTs7Ozs7Ozs7Ozs7a0NBRW5CLDhEQUFDWTt3QkFBR1osV0FBVTtrQ0FBa0M7Ozs7OztrQ0FDaEQsOERBQUNhO3dCQUFFYixXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJckM7SUFFQSxxQkFDRSw4REFBQ0k7UUFBSUosV0FBVTs7WUFFWjVCLGdDQUNDLDhEQUFDZ0M7Z0JBQUlKLFdBQVU7O2tDQUNiLDhEQUFDWTt3QkFBR1osV0FBVTs7MENBQ1osOERBQUN4QyxpSUFBS0E7Z0NBQUN3QyxXQUFVOzs7Ozs7NEJBQWlDOzs7Ozs7O2tDQUlwRCw4REFBQ0k7d0JBQUlKLFdBQVU7OzBDQUNiLDhEQUFDSTs7a0RBQ0MsOERBQUNVO3dDQUFHZCxXQUFVO2tEQUFxQjs7Ozs7O2tEQUNuQyw4REFBQ0k7d0NBQUlKLFdBQVcsbURBQXdGLE9BQXJDRSxhQUFhOUIsZUFBZXNCLE9BQU87OzBEQUNwRyw4REFBQzdCLGlJQUFHQTtnREFBQ21DLFdBQVU7Ozs7OzswREFDZiw4REFBQ2U7Z0RBQUtmLFdBQVU7MERBQ2I1QixlQUFlc0IsT0FBTyxLQUFLLFFBQVEsaUJBQ25DdEIsZUFBZXNCLE9BQU8sS0FBSyxXQUFXLGlCQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUs5RCw4REFBQ1U7O2tEQUNDLDhEQUFDVTt3Q0FBR2QsV0FBVTtrREFBcUI7Ozs7OztrREFDbkMsOERBQUNnQjt3Q0FBR2hCLFdBQVU7a0RBQ1g1QixlQUFldUIsZUFBZSxDQUFDc0IsR0FBRyxDQUFDLENBQUNDLEtBQUtDLHNCQUN4Qyw4REFBQ0M7Z0RBQWVwQixXQUFVOztrRUFDeEIsOERBQUNyQyxpSUFBV0E7d0RBQUNxQyxXQUFVOzs7Ozs7b0RBQ3RCa0I7OytDQUZNQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFZckIsOERBQUNmO2dCQUFJSixXQUFVOztrQ0FDYiw4REFBQ1k7d0JBQUdaLFdBQVU7OzBDQUNaLDhEQUFDdkMsaUlBQVVBO2dDQUFDdUMsV0FBVTs7Ozs7OzRCQUErQjs7Ozs7OztrQ0FJdkQsOERBQUNJO3dCQUFJSixXQUFVO2tDQUNaOUIsU0FBUytDLEdBQUcsQ0FBQyxDQUFDSSxTQUFTRixzQkFDdEIsOERBQUNyRCxpREFBTUEsQ0FBQ3NDLEdBQUc7Z0NBRVRrQixTQUFTO29DQUFFQyxTQUFTO29DQUFHQyxHQUFHO2dDQUFHO2dDQUM3Qm5CLFNBQVM7b0NBQUVrQixTQUFTO29DQUFHQyxHQUFHO2dDQUFFO2dDQUM1QmpCLFlBQVk7b0NBQUVrQixPQUFPTixRQUFRO2dDQUFJO2dDQUNqQ25CLFdBQVcseUJBQXVELE9BQTlCQyxnQkFBZ0JvQixRQUFRbEMsSUFBSTswQ0FFaEUsNEVBQUNpQjtvQ0FBSUosV0FBVTs7c0RBQ2IsOERBQUNJOzRDQUFJSixXQUFVOztnREFDWkQsZUFBZXNCLFFBQVFsQyxJQUFJOzhEQUM1Qiw4REFBQ2lCO29EQUFJSixXQUFVOztzRUFDYiw4REFBQ2M7NERBQUdkLFdBQVU7c0VBQWlCcUIsUUFBUWpDLEtBQUs7Ozs7OztzRUFDNUMsOERBQUN5Qjs0REFBRWIsV0FBVTtzRUFBMkJxQixRQUFRaEMsV0FBVzs7Ozs7O3NFQUMzRCw4REFBQ2U7NERBQUlKLFdBQVU7OzhFQUNiLDhEQUFDYTtvRUFBRWIsV0FBVTs4RUFBc0I7Ozs7Ozs4RUFDbkMsOERBQUNhO29FQUFFYixXQUFVOzhFQUFXcUIsUUFBUS9CLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFJcEQsOERBQUNjOzRDQUFJSixXQUFVOztnREFBd0I7Z0RBQy9CcUIsQ0FBQUEsUUFBUTlCLFVBQVUsR0FBRyxHQUFFLEVBQUdFLE9BQU8sQ0FBQztnREFBRzs7Ozs7Ozs7Ozs7OzsrQkFuQjFDNEIsUUFBUW5DLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBNEJ2Qiw4REFBQ2tCO2dCQUFJSixXQUFVOzBCQUNiLDRFQUFDSTtvQkFBSUosV0FBVTs7c0NBQ2IsOERBQUN4QyxpSUFBS0E7NEJBQUN3QyxXQUFVOzs7Ozs7c0NBQ2pCLDhEQUFDSTs7OENBQ0MsOERBQUNVO29DQUFHZCxXQUFVOzhDQUFnQzs7Ozs7OzhDQUM5Qyw4REFBQ2E7b0NBQUViLFdBQVU7OENBQStCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVN4RDtHQXJSd0JqQztLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9IZWFsdGhBbmFseXRpY3MudHN4P2E4MGQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQnJhaW4sIFRyZW5kaW5nVXAsIEFsZXJ0VHJpYW5nbGUsIENoZWNrQ2lyY2xlLCBUYXJnZXQsIFphcCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBQcmVnbmFuY3lSZXN1bHRzIH0gZnJvbSAnQC91dGlscy9wcmVnbmFuY3lDYWxjdWxhdGlvbnMnO1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5cbmludGVyZmFjZSBIZWFsdGhBbmFseXRpY3NQcm9wcyB7XG4gIHJlc3VsdHM6IFByZWduYW5jeVJlc3VsdHM7XG4gIGxhc3RNZW5zdHJ1YWxQZXJpb2Q6IERhdGU7XG59XG5cbmludGVyZmFjZSBIZWFsdGhJbnNpZ2h0IHtcbiAgaWQ6IHN0cmluZztcbiAgdHlwZTogJ3Bvc2l0aXZlJyB8ICd3YXJuaW5nJyB8ICduZXV0cmFsJyB8ICdjcml0aWNhbCc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIHJlY29tbWVuZGF0aW9uOiBzdHJpbmc7XG4gIGNvbmZpZGVuY2U6IG51bWJlcjtcbiAgY2F0ZWdvcnk6ICdudXRyaXRpb24nIHwgJ2V4ZXJjaXNlJyB8ICdtZWRpY2FsJyB8ICdsaWZlc3R5bGUnO1xufVxuXG5pbnRlcmZhY2UgUmlza0Fzc2Vzc21lbnQge1xuICBvdmVyYWxsOiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnO1xuICBmYWN0b3JzOiB7XG4gICAgYWdlPzogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJztcbiAgICB3ZWlnaHQ/OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnO1xuICAgIGxpZmVzdHlsZT86ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCc7XG4gICAgbWVkaWNhbD86ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCc7XG4gIH07XG4gIHJlY29tbWVuZGF0aW9uczogc3RyaW5nW107XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhlYWx0aEFuYWx5dGljcyh7IHJlc3VsdHMsIGxhc3RNZW5zdHJ1YWxQZXJpb2QgfTogSGVhbHRoQW5hbHl0aWNzUHJvcHMpIHtcbiAgY29uc3QgW2luc2lnaHRzLCBzZXRJbnNpZ2h0c10gPSB1c2VTdGF0ZTxIZWFsdGhJbnNpZ2h0W10+KFtdKTtcbiAgY29uc3QgW3Jpc2tBc3Nlc3NtZW50LCBzZXRSaXNrQXNzZXNzbWVudF0gPSB1c2VTdGF0ZTxSaXNrQXNzZXNzbWVudCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNBbmFseXppbmcsIHNldElzQW5hbHl6aW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgYW5hbHl6ZUhlYWx0aERhdGEoKTtcbiAgfSwgW3Jlc3VsdHMsIGxhc3RNZW5zdHJ1YWxQZXJpb2RdKTtcblxuICBjb25zdCBhbmFseXplSGVhbHRoRGF0YSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRJc0FuYWx5emluZyh0cnVlKTtcbiAgICBcbiAgICAvLyDZhdit2KfZg9in2Kkg2KrYrdmE2YrZhCDYp9mE2KjZitin2YbYp9iqXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBjb25zdCBnZW5lcmF0ZWRJbnNpZ2h0cyA9IGdlbmVyYXRlSGVhbHRoSW5zaWdodHMoKTtcbiAgICAgIGNvbnN0IGFzc2Vzc21lbnQgPSBnZW5lcmF0ZVJpc2tBc3Nlc3NtZW50KCk7XG4gICAgICBcbiAgICAgIHNldEluc2lnaHRzKGdlbmVyYXRlZEluc2lnaHRzKTtcbiAgICAgIHNldFJpc2tBc3Nlc3NtZW50KGFzc2Vzc21lbnQpO1xuICAgICAgc2V0SXNBbmFseXppbmcoZmFsc2UpO1xuICAgIH0sIDIwMDApO1xuICB9O1xuXG4gIGNvbnN0IGdlbmVyYXRlSGVhbHRoSW5zaWdodHMgPSAoKTogSGVhbHRoSW5zaWdodFtdID0+IHtcbiAgICBjb25zdCBpbnNpZ2h0czogSGVhbHRoSW5zaWdodFtdID0gW107XG4gICAgY29uc3QgeyBjdXJyZW50V2Vla3MsIHRyaW1lc3RlciwgcHJvZ3Jlc3NQZXJjZW50YWdlIH0gPSByZXN1bHRzO1xuXG4gICAgLy8g2KrYrdmE2YrZhCDZhdix2K3ZhNipINin2YTYrdmF2YRcbiAgICBpZiAodHJpbWVzdGVyID09PSAxKSB7XG4gICAgICBpbnNpZ2h0cy5wdXNoKHtcbiAgICAgICAgaWQ6ICd0cmltZXN0ZXIxLW51dHJpdGlvbicsXG4gICAgICAgIHR5cGU6ICdwb3NpdGl2ZScsXG4gICAgICAgIHRpdGxlOiAn2KfZhNiq2LrYsNmK2Kkg2YHZiiDYp9mE2KvZhNirINin2YTYo9mI2YQgLSDZhdix2K3ZhNipINit2KfYs9mF2KknLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ9ij2YbYqiDZgdmKINij2YfZhSDZhdix2K3ZhNipINmE2KrZg9mI2YrZhiDYp9mE2KPYudi22KfYoSDYp9mE2K3ZitmI2YrYqSDZhNmE2KzZhtmK2YYnLFxuICAgICAgICByZWNvbW1lbmRhdGlvbjogJ9ix2YPYstmKINi52YTZiSDYrdmF2LYg2KfZhNmB2YjZhNmK2YMgKDQwMC04MDAg2YXZitmD2LHZiNis2LHYp9mFKdiMINin2YTYrdiv2YrYr9iMINmI2KfZhNmD2KfZhNiz2YrZiNmFLiDYqtis2YbYqNmKINin2YTZg9in2YHZitmK2YYg2KfZhNiy2KfYptivINmI2KfZhNij2LfYudmF2Kkg2KfZhNmG2YrYptipJyxcbiAgICAgICAgY29uZmlkZW5jZTogMC45NSxcbiAgICAgICAgY2F0ZWdvcnk6ICdudXRyaXRpb24nXG4gICAgICB9KTtcblxuICAgICAgaWYgKGN1cnJlbnRXZWVrcyA+PSA2ICYmIGN1cnJlbnRXZWVrcyA8PSAxMikge1xuICAgICAgICBpbnNpZ2h0cy5wdXNoKHtcbiAgICAgICAgICBpZDogJ21vcm5pbmctc2lja25lc3MnLFxuICAgICAgICAgIHR5cGU6ICduZXV0cmFsJyxcbiAgICAgICAgICB0aXRsZTogJ9il2K/Yp9ix2Kkg2KPYudix2KfYtiDYp9mE2K3ZhdmEINin2YTZhdio2YPYsdipJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ9in2YTYutir2YrYp9mGINmI2KfZhNiq2LnYqCDYtNin2KbYudin2YYg2YHZiiDZh9iw2Ycg2KfZhNmF2LHYrdmE2KknLFxuICAgICAgICAgIHJlY29tbWVuZGF0aW9uOiAn2KrZhtin2YjZhNmKINmI2KzYqNin2Kog2LXYutmK2LHYqSDZhdiq2YPYsdix2Kkg2YjYp9i02LHYqNmKINin2YTYstmG2KzYqNmK2YQnLFxuICAgICAgICAgIGNvbmZpZGVuY2U6IDAuODUsXG4gICAgICAgICAgY2F0ZWdvcnk6ICdsaWZlc3R5bGUnXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAodHJpbWVzdGVyID09PSAyKSB7XG4gICAgICBpbnNpZ2h0cy5wdXNoKHtcbiAgICAgICAgaWQ6ICd0cmltZXN0ZXIyLWV4ZXJjaXNlJyxcbiAgICAgICAgdHlwZTogJ3Bvc2l0aXZlJyxcbiAgICAgICAgdGl0bGU6ICfYp9mE2KvZhNirINin2YTYq9in2YbZiiAtINmB2KrYsdipINin2YTYt9in2YLYqScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAn2YfYsNmHINij2YHYttmEINmB2KrYsdipINmE2YXZhdin2LHYs9ipINin2YTYsdmK2KfYttipINmI2KfZhNij2YbYtNi32KknLFxuICAgICAgICByZWNvbW1lbmRhdGlvbjogJ9in2KjYr9im2Yog2KjYsdmG2KfZhdisINiq2YXYp9ix2YrZhiDYotmF2YYg2YTZhNit2YXZhCcsXG4gICAgICAgIGNvbmZpZGVuY2U6IDAuODgsXG4gICAgICAgIGNhdGVnb3J5OiAnZXhlcmNpc2UnXG4gICAgICB9KTtcblxuICAgICAgaWYgKGN1cnJlbnRXZWVrcyA+PSAxOCAmJiBjdXJyZW50V2Vla3MgPD0gMjIpIHtcbiAgICAgICAgaW5zaWdodHMucHVzaCh7XG4gICAgICAgICAgaWQ6ICdhbmF0b215LXNjYW4nLFxuICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJyxcbiAgICAgICAgICB0aXRsZTogJ9mB2K3YtSDYp9mE2YXZiNis2KfYqiDZgdmI2YIg2KfZhNi12YjYqtmK2Kkg2KfZhNmF2YHYtdmEJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ9mI2YLYqiDZhdmH2YUg2YTZgdit2LUg2KrYt9mI2LEg2KfZhNis2YbZitmGJyxcbiAgICAgICAgICByZWNvbW1lbmRhdGlvbjogJ9in2K3YrNiy2Yog2YXZiNi52K8g2YHYrdi1INin2YTZhdmI2KzYp9iqINmB2YjZgiDYp9mE2LXZiNiq2YrYqScsXG4gICAgICAgICAgY29uZmlkZW5jZTogMC45NSxcbiAgICAgICAgICBjYXRlZ29yeTogJ21lZGljYWwnXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBpbnNpZ2h0cy5wdXNoKHtcbiAgICAgICAgaWQ6ICd0cmltZXN0ZXIzLXByZXBhcmF0aW9uJyxcbiAgICAgICAgdHlwZTogJ3dhcm5pbmcnLFxuICAgICAgICB0aXRsZTogJ9in2YTYp9iz2KrYudiv2KfYryDZhNmE2YjZhNin2K/YqScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAn2KfZhNir2YTYqyDYp9mE2KPYrtmK2LEgLSDZiNmC2Kog2KfZhNiq2K3YttmK2LEnLFxuICAgICAgICByZWNvbW1lbmRhdGlvbjogJ9in2KjYr9im2Yog2KjYrdi22YjYsSDYr9mI2LHYp9iqINin2YTZiNmE2KfYr9ipINmI2KrYrdi22YrYsSDYrdmC2YrYqNipINin2YTZhdiz2KrYtNmB2YknLFxuICAgICAgICBjb25maWRlbmNlOiAwLjkyLFxuICAgICAgICBjYXRlZ29yeTogJ2xpZmVzdHlsZSdcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoY3VycmVudFdlZWtzID49IDM2KSB7XG4gICAgICAgIGluc2lnaHRzLnB1c2goe1xuICAgICAgICAgIGlkOiAnZnVsbC10ZXJtLXByZXAnLFxuICAgICAgICAgIHR5cGU6ICdjcml0aWNhbCcsXG4gICAgICAgICAgdGl0bGU6ICfYp9mC2KrYsdin2Kgg2YXZiNi52K8g2KfZhNmI2YTYp9iv2KknLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAn2KfZhNis2YbZitmGINmF2YPYqtmF2YQg2KfZhNmG2YXZiCDYqtmC2LHZitio2KfZiycsXG4gICAgICAgICAgcmVjb21tZW5kYXRpb246ICfZg9mI2YbZiiDZhdiz2KrYudiv2Kkg2YTZhNmI2YTYp9iv2Kkg2YHZiiDYo9mKINmI2YLYqicsXG4gICAgICAgICAgY29uZmlkZW5jZTogMC45OCxcbiAgICAgICAgICBjYXRlZ29yeTogJ21lZGljYWwnXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vINiq2K3ZhNmK2YQg2KfZhNiq2YLYr9mFXG4gICAgaWYgKHByb2dyZXNzUGVyY2VudGFnZSA+IDUwKSB7XG4gICAgICBpbnNpZ2h0cy5wdXNoKHtcbiAgICAgICAgaWQ6ICdwcm9ncmVzcy1taWxlc3RvbmUnLFxuICAgICAgICB0eXBlOiAncG9zaXRpdmUnLFxuICAgICAgICB0aXRsZTogJ9iq2KzYp9mI2LLYqiDZhdmG2KrYtdmBINin2YTYrdmF2YQnLFxuICAgICAgICBkZXNjcmlwdGlvbjogYNij2YPZhdmE2KogJHtwcm9ncmVzc1BlcmNlbnRhZ2UudG9GaXhlZCgwKX0lINmF2YYg2LHYrdmE2Kkg2KfZhNit2YXZhGAsXG4gICAgICAgIHJlY29tbWVuZGF0aW9uOiAn2KfYs9iq2YXYsdmKINmB2Yog2KfZhNmF2KrYp9io2LnYqSDYp9mE2LfYqNmK2Kkg2KfZhNmF2YbYqti42YXYqScsXG4gICAgICAgIGNvbmZpZGVuY2U6IDAuOSxcbiAgICAgICAgY2F0ZWdvcnk6ICdtZWRpY2FsJ1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGluc2lnaHRzO1xuICB9O1xuXG4gIGNvbnN0IGdlbmVyYXRlUmlza0Fzc2Vzc21lbnQgPSAoKTogUmlza0Fzc2Vzc21lbnQgPT4ge1xuICAgIGNvbnN0IHsgY3VycmVudFdlZWtzLCB0cmltZXN0ZXIgfSA9IHJlc3VsdHM7XG4gICAgXG4gICAgLy8g2KrZgtmK2YrZhSDYp9mE2YXYrtin2LfYsSDYqNmG2KfYodmLINi52YTZiSDYp9mE2KjZitin2YbYp9iqINin2YTZhdiq2KfYrdipXG4gICAgbGV0IG92ZXJhbGw6ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCcgPSAnbG93JztcbiAgICBjb25zdCByZWNvbW1lbmRhdGlvbnM6IHN0cmluZ1tdID0gW107XG5cbiAgICBpZiAodHJpbWVzdGVyID09PSAxKSB7XG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgn2KrZhtin2YjZhNmKINit2YXYtiDYp9mE2YHZiNmE2YrZgyDZitmI2YXZitin2YsnKTtcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKCfYqtis2YbYqNmKINin2YTZg9it2YjZhCDZiNin2YTYqtiv2K7ZitmGJyk7XG4gICAgfSBlbHNlIGlmICh0cmltZXN0ZXIgPT09IDIpIHtcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKCfZhdin2LHYs9mKINin2YTYsdmK2KfYttipINin2YTYrtmB2YrZgdipJyk7XG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgn2LHYp9mC2KjZiiDYstmK2KfYr9ipINin2YTZiNiy2YYnKTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ9ix2KfZgtio2Yog2K3YsdmD2Kkg2KfZhNis2YbZitmGINmK2YjZhdmK2KfZiycpO1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ9in2LPYqti52K/ZiiDZhNmE2YjZhNin2K/YqScpO1xuICAgICAgaWYgKGN1cnJlbnRXZWVrcyA+IDQyKSB7XG4gICAgICAgIG92ZXJhbGwgPSAnbWVkaXVtJztcbiAgICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ9in2LPYqti02YrYsdmKINin2YTYt9io2YrYqCDZgdmI2LHYp9mLIC0g2KrYo9iu2LEg2KfZhNmI2YTYp9iv2KknKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgb3ZlcmFsbCxcbiAgICAgIGZhY3RvcnM6IHtcbiAgICAgICAgbWVkaWNhbDogJ2xvdycsXG4gICAgICAgIGxpZmVzdHlsZTogJ2xvdydcbiAgICAgIH0sXG4gICAgICByZWNvbW1lbmRhdGlvbnNcbiAgICB9O1xuICB9O1xuXG4gIGNvbnN0IGdldEluc2lnaHRJY29uID0gKHR5cGU6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSAncG9zaXRpdmUnOiByZXR1cm4gPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmVlbi01MDBcIiAvPjtcbiAgICAgIGNhc2UgJ3dhcm5pbmcnOiByZXR1cm4gPEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXllbGxvdy01MDBcIiAvPjtcbiAgICAgIGNhc2UgJ2NyaXRpY2FsJzogcmV0dXJuIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1yZWQtNTAwXCIgLz47XG4gICAgICBkZWZhdWx0OiByZXR1cm4gPFRhcmdldCBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtYmx1ZS01MDBcIiAvPjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0SW5zaWdodENvbG9yID0gKHR5cGU6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSAncG9zaXRpdmUnOiByZXR1cm4gJ2JnLWdyZWVuLTUwIGJvcmRlci1ncmVlbi0yMDAnO1xuICAgICAgY2FzZSAnd2FybmluZyc6IHJldHVybiAnYmcteWVsbG93LTUwIGJvcmRlci15ZWxsb3ctMjAwJztcbiAgICAgIGNhc2UgJ2NyaXRpY2FsJzogcmV0dXJuICdiZy1yZWQtNTAgYm9yZGVyLXJlZC0yMDAnO1xuICAgICAgZGVmYXVsdDogcmV0dXJuICdiZy1ibHVlLTUwIGJvcmRlci1ibHVlLTIwMCc7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldFJpc2tDb2xvciA9IChyaXNrOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHJpc2spIHtcbiAgICAgIGNhc2UgJ2hpZ2gnOiByZXR1cm4gJ3RleHQtcmVkLTYwMCBiZy1yZWQtMTAwJztcbiAgICAgIGNhc2UgJ21lZGl1bSc6IHJldHVybiAndGV4dC15ZWxsb3ctNjAwIGJnLXllbGxvdy0xMDAnO1xuICAgICAgZGVmYXVsdDogcmV0dXJuICd0ZXh0LWdyZWVuLTYwMCBiZy1ncmVlbi0xMDAnO1xuICAgIH1cbiAgfTtcblxuICBpZiAoaXNBbmFseXppbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgYW5pbWF0ZT17eyByb3RhdGU6IDM2MCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMiwgcmVwZWF0OiBJbmZpbml0eSwgZWFzZTogXCJsaW5lYXJcIiB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8QnJhaW4gY2xhc3NOYW1lPVwidy0xMiBoLTEyIHRleHQtcHVycGxlLTUwMFwiIC8+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbXQtNCBtYi0yXCI+2KrYrdmE2YrZhCDYp9mE2KjZitin2YbYp9iqINin2YTYtdit2YrYqTwvaDM+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPtin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52Yog2YrYrdmE2YQg2KjZitin2YbYp9iq2YMuLi48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHsvKiDYqtmC2YrZitmFINin2YTZhdiu2KfYt9ixINin2YTYudin2YUgKi99XG4gICAgICB7cmlza0Fzc2Vzc21lbnQgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPEJyYWluIGNsYXNzTmFtZT1cInctNiBoLTYgbWwtMiB0ZXh0LXB1cnBsZS01MDBcIiAvPlxuICAgICAgICAgICAg2KrZgtmK2YrZhSDYp9mE2YXYrtin2LfYsSDYp9mE2LDZg9mKXG4gICAgICAgICAgPC9oMz5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIG1iLTNcIj7Yp9mE2KrZgtmK2YrZhSDYp9mE2LnYp9mFPC9oND5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIHJvdW5kZWQtZnVsbCAke2dldFJpc2tDb2xvcihyaXNrQXNzZXNzbWVudC5vdmVyYWxsKX1gfT5cbiAgICAgICAgICAgICAgICA8WmFwIGNsYXNzTmFtZT1cInctNCBoLTQgbWwtMVwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgIHtyaXNrQXNzZXNzbWVudC5vdmVyYWxsID09PSAnbG93JyA/ICfZhdiu2KfYt9ixINmF2YbYrtmB2LbYqScgOlxuICAgICAgICAgICAgICAgICAgIHJpc2tBc3Nlc3NtZW50Lm92ZXJhbGwgPT09ICdtZWRpdW0nID8gJ9mF2K7Yp9i32LEg2YXYqtmI2LPYt9ipJyA6ICfZhdiu2KfYt9ixINi52KfZhNmK2KknfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgbWItM1wiPtin2YTYqtmI2LXZitin2Kog2KfZhNiw2YPZitipPC9oND5cbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMSB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAge3Jpc2tBc3Nlc3NtZW50LnJlY29tbWVuZGF0aW9ucy5tYXAoKHJlYywgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LWdyZWVuLTUwMCBtbC0xIGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICAgICAgICAgICB7cmVjfVxuICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDYp9mE2LHYpNmJINin2YTYtdit2YrYqSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cInctNiBoLTYgbWwtMiB0ZXh0LWJsdWUtNTAwXCIgLz5cbiAgICAgICAgICDYp9mE2LHYpNmJINin2YTYtdit2YrYqSDYp9mE2YXYrti12LXYqVxuICAgICAgICA8L2gzPlxuICAgICAgICBcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC00XCI+XG4gICAgICAgICAge2luc2lnaHRzLm1hcCgoaW5zaWdodCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGtleT17aW5zaWdodC5pZH1cbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYm9yZGVyIHJvdW5kZWQtbGcgcC00ICR7Z2V0SW5zaWdodENvbG9yKGluc2lnaHQudHlwZSl9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTMgc3BhY2UteC1yZXZlcnNlXCI+XG4gICAgICAgICAgICAgICAgICB7Z2V0SW5zaWdodEljb24oaW5zaWdodC50eXBlKX1cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+e2luc2lnaHQudGl0bGV9PC9oND5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBtdC0xIG9wYWNpdHktODBcIj57aW5zaWdodC5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMiBwLTIgYmctd2hpdGUgYmctb3BhY2l0eS01MCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPvCfkqEg2KfZhNiq2YjYtdmK2Kk6PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc21cIj57aW5zaWdodC5yZWNvbW1lbmRhdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgINiv2YLYqSB7KGluc2lnaHQuY29uZmlkZW5jZSAqIDEwMCkudG9GaXhlZCgwKX0lXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog2YXZhNin2K3YuNipINmF2YfZhdipICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNTAgdG8tcGluay01MFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICA8QnJhaW4gY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXB1cnBsZS01MDBcIiAvPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXB1cnBsZS05MDBcIj7Yqtit2YTZitmEINiw2YPZiiDZhdiu2LXYtTwvaDQ+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHVycGxlLTcwMCBtdC0xXCI+XG4gICAgICAgICAgICAgINmH2LDYpyDYp9mE2KrYrdmE2YrZhCDZhdio2YbZiiDYudmE2Ykg2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZiiDZiNio2YrYp9mG2KfYqtmDINin2YTYtNiu2LXZitipLiBcbiAgICAgICAgICAgICAg2KfYs9iq2LTZitix2Yog2LfYqNmK2KjZgyDYr9in2KbZhdin2Ysg2YTZhNit2LXZiNmEINi52YTZiSDZhti12KfYptitINi32KjZitipINiv2YLZitmC2KkuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJCcmFpbiIsIlRyZW5kaW5nVXAiLCJBbGVydFRyaWFuZ2xlIiwiQ2hlY2tDaXJjbGUiLCJUYXJnZXQiLCJaYXAiLCJtb3Rpb24iLCJIZWFsdGhBbmFseXRpY3MiLCJyZXN1bHRzIiwibGFzdE1lbnN0cnVhbFBlcmlvZCIsImluc2lnaHRzIiwic2V0SW5zaWdodHMiLCJyaXNrQXNzZXNzbWVudCIsInNldFJpc2tBc3Nlc3NtZW50IiwiaXNBbmFseXppbmciLCJzZXRJc0FuYWx5emluZyIsImFuYWx5emVIZWFsdGhEYXRhIiwic2V0VGltZW91dCIsImdlbmVyYXRlZEluc2lnaHRzIiwiZ2VuZXJhdGVIZWFsdGhJbnNpZ2h0cyIsImFzc2Vzc21lbnQiLCJnZW5lcmF0ZVJpc2tBc3Nlc3NtZW50IiwiY3VycmVudFdlZWtzIiwidHJpbWVzdGVyIiwicHJvZ3Jlc3NQZXJjZW50YWdlIiwicHVzaCIsImlkIiwidHlwZSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJyZWNvbW1lbmRhdGlvbiIsImNvbmZpZGVuY2UiLCJjYXRlZ29yeSIsInRvRml4ZWQiLCJvdmVyYWxsIiwicmVjb21tZW5kYXRpb25zIiwiZmFjdG9ycyIsIm1lZGljYWwiLCJsaWZlc3R5bGUiLCJnZXRJbnNpZ2h0SWNvbiIsImNsYXNzTmFtZSIsImdldEluc2lnaHRDb2xvciIsImdldFJpc2tDb2xvciIsInJpc2siLCJkaXYiLCJhbmltYXRlIiwicm90YXRlIiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwicmVwZWF0IiwiSW5maW5pdHkiLCJlYXNlIiwiaDMiLCJwIiwiaDQiLCJzcGFuIiwidWwiLCJtYXAiLCJyZWMiLCJpbmRleCIsImxpIiwiaW5zaWdodCIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImRlbGF5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/HealthAnalytics.tsx\n"));

/***/ })

});