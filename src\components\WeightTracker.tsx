'use client';

import { useState, useEffect } from 'react';
import { Scale, TrendingUp, Target, AlertTriangle, CheckCircle } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';
import { motion } from 'framer-motion';

interface WeightEntry {
  id: string;
  date: Date;
  weight: number;
  week: number;
}

interface WeightTrackerProps {
  pregnancyWeek: number;
  lastMenstrualPeriod: Date;
}

export default function WeightTracker({ pregnancyWeek, lastMenstrualPeriod }: WeightTrackerProps) {
  const [weightEntries, setWeightEntries] = useState<WeightEntry[]>([]);
  const [currentWeight, setCurrentWeight] = useState('');
  const [prePregnancyWeight, setPrePregnancyWeight] = useState('');
  const [height, setHeight] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);

  // حساب BMI قبل الحمل
  const calculateBMI = (weight: number, height: number) => {
    const heightInMeters = height / 100;
    return weight / (heightInMeters * heightInMeters);
  };

  // حساب زيادة الوزن المثلى حسب BMI
  const getOptimalWeightGain = (bmi: number) => {
    if (bmi < 18.5) {
      return { min: 12.5, max: 18, category: 'نحيف' };
    } else if (bmi < 25) {
      return { min: 11.5, max: 16, category: 'طبيعي' };
    } else if (bmi < 30) {
      return { min: 7, max: 11.5, category: 'زيادة وزن' };
    } else {
      return { min: 5, max: 9, category: 'سمنة' };
    }
  };

  // حساب زيادة الوزن المتوقعة حسب الأسبوع
  const getExpectedWeightGain = (week: number, totalGain: { min: number; max: number }) => {
    if (week <= 12) {
      return { min: 0.5, max: 2 }; // الثلث الأول
    } else {
      const remainingWeeks = week - 12;
      const weeklyGain = {
        min: (totalGain.min - 1.5) / 28,
        max: (totalGain.max - 1.5) / 28
      };
      return {
        min: 1.5 + (remainingWeeks * weeklyGain.min),
        max: 1.5 + (remainingWeeks * weeklyGain.max)
      };
    }
  };

  const addWeightEntry = () => {
    if (!currentWeight || !prePregnancyWeight) return;

    const newEntry: WeightEntry = {
      id: Date.now().toString(),
      date: new Date(),
      weight: parseFloat(currentWeight),
      week: pregnancyWeek
    };

    setWeightEntries(prev => [...prev, newEntry].sort((a, b) => a.week - b.week));
    setCurrentWeight('');
    setShowAddForm(false);
  };

  const bmi = prePregnancyWeight && height ? 
    calculateBMI(parseFloat(prePregnancyWeight), parseFloat(height)) : 0;
  
  const optimalGain = bmi ? getOptimalWeightGain(bmi) : null;
  const expectedGain = optimalGain ? getExpectedWeightGain(pregnancyWeek, optimalGain) : null;
  
  const currentGain = weightEntries.length > 0 && prePregnancyWeight ? 
    weightEntries[weightEntries.length - 1].weight - parseFloat(prePregnancyWeight) : 0;

  // تحضير بيانات الرسم البياني
  const chartData = weightEntries.map(entry => ({
    week: entry.week,
    weight: entry.weight,
    gain: entry.weight - parseFloat(prePregnancyWeight || '0'),
    optimalMin: expectedGain ? getExpectedWeightGain(entry.week, optimalGain!).min : 0,
    optimalMax: expectedGain ? getExpectedWeightGain(entry.week, optimalGain!).max : 0
  }));

  const getWeightStatus = () => {
    if (!expectedGain || !currentGain) return 'unknown';
    
    if (currentGain < expectedGain.min) return 'low';
    if (currentGain > expectedGain.max) return 'high';
    return 'normal';
  };

  const weightStatus = getWeightStatus();

  return (
    <div className="space-y-6">
      {/* إعداد البيانات الأساسية */}
      {(!prePregnancyWeight || !height) && (
        <div className="card">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <Scale className="w-6 h-6 ml-2 text-blue-500" />
            إعداد متتبع الوزن
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">الوزن قبل الحمل (كجم)</label>
              <input
                type="number"
                value={prePregnancyWeight}
                onChange={(e) => setPrePregnancyWeight(e.target.value)}
                className="input-field"
                placeholder="مثال: 65"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">الطول (سم)</label>
              <input
                type="number"
                value={height}
                onChange={(e) => setHeight(e.target.value)}
                className="input-field"
                placeholder="مثال: 165"
              />
            </div>
          </div>
        </div>
      )}

      {prePregnancyWeight && height && (
        <>
          {/* معلومات BMI والزيادة المثلى */}
          <div className="card">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <Target className="w-6 h-6 ml-2 text-green-500" />
              معلومات الوزن المثلى
            </h3>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-xl">
                <h4 className="font-semibold text-blue-800">مؤشر كتلة الجسم</h4>
                <p className="text-2xl font-bold text-blue-600">{bmi.toFixed(1)}</p>
                <p className="text-sm text-blue-600">{optimalGain?.category}</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-xl">
                <h4 className="font-semibold text-green-800">الزيادة المثلى</h4>
                <p className="text-2xl font-bold text-green-600">
                  {optimalGain?.min}-{optimalGain?.max} كجم
                </p>
                <p className="text-sm text-green-600">طوال الحمل</p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-xl">
                <h4 className="font-semibold text-purple-800">المتوقع حالياً</h4>
                <p className="text-2xl font-bold text-purple-600">
                  {expectedGain?.min.toFixed(1)}-{expectedGain?.max.toFixed(1)} كجم
                </p>
                <p className="text-sm text-purple-600">حتى الأسبوع {pregnancyWeek}</p>
              </div>
            </div>
          </div>

          {/* حالة الوزن الحالية */}
          {currentGain > 0 && (
            <div className={`card ${
              weightStatus === 'normal' ? 'bg-green-50 border-green-200' :
              weightStatus === 'low' ? 'bg-yellow-50 border-yellow-200' :
              'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  {weightStatus === 'normal' ? (
                    <CheckCircle className="w-8 h-8 text-green-500 ml-3" />
                  ) : (
                    <AlertTriangle className="w-8 h-8 text-yellow-500 ml-3" />
                  )}
                  <div>
                    <h4 className="font-bold text-lg">
                      {weightStatus === 'normal' ? 'زيادة الوزن مثالية' :
                       weightStatus === 'low' ? 'زيادة الوزن أقل من المطلوب' :
                       'زيادة الوزن أكثر من المطلوب'}
                    </h4>
                    <p className="text-gray-600">
                      زيادة حالية: {currentGain.toFixed(1)} كجم
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold">
                    {((currentGain / (expectedGain?.max || 1)) * 100).toFixed(0)}%
                  </p>
                  <p className="text-sm text-gray-600">من المتوقع</p>
                </div>
              </div>
            </div>
          )}

          {/* إضافة قياس جديد */}
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold flex items-center">
                <TrendingUp className="w-6 h-6 ml-2 text-purple-500" />
                تتبع الوزن
              </h3>
              <button
                onClick={() => setShowAddForm(!showAddForm)}
                className="btn-primary"
              >
                إضافة قياس
              </button>
            </div>

            {showAddForm && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="mb-6 p-4 bg-gray-50 rounded-xl"
              >
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="flex-1">
                    <label className="block text-sm font-medium mb-2">الوزن الحالي (كجم)</label>
                    <input
                      type="number"
                      value={currentWeight}
                      onChange={(e) => setCurrentWeight(e.target.value)}
                      className="input-field"
                      placeholder="مثال: 70"
                    />
                  </div>
                  <div className="flex space-x-2 space-x-reverse">
                    <button
                      onClick={addWeightEntry}
                      disabled={!currentWeight}
                      className="btn-primary disabled:opacity-50"
                    >
                      إضافة
                    </button>
                    <button
                      onClick={() => setShowAddForm(false)}
                      className="btn-secondary"
                    >
                      إلغاء
                    </button>
                  </div>
                </div>
              </motion.div>
            )}

            {/* الرسم البياني */}
            {chartData.length > 0 && (
              <div className="h-80 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                    <XAxis 
                      dataKey="week" 
                      stroke="#6b7280"
                      label={{ value: 'أسبوع الحمل', position: 'insideBottom', offset: -10 }}
                    />
                    <YAxis 
                      stroke="#6b7280"
                      label={{ value: 'زيادة الوزن (كجم)', angle: 90, position: 'insideLeft' }}
                    />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        border: 'none',
                        borderRadius: '12px',
                        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
                      }}
                      labelFormatter={(value) => `الأسبوع ${value}`}
                    />
                    <ReferenceLine 
                      y={expectedGain?.min || 0} 
                      stroke="#10b981" 
                      strokeDasharray="5 5"
                      label="الحد الأدنى"
                    />
                    <ReferenceLine 
                      y={expectedGain?.max || 0} 
                      stroke="#ef4444" 
                      strokeDasharray="5 5"
                      label="الحد الأعلى"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="gain" 
                      stroke="#8b5cf6" 
                      strokeWidth={3}
                      dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 6 }}
                      activeDot={{ r: 8, fill: '#8b5cf6' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            )}

            {/* قائمة القياسات */}
            {weightEntries.length > 0 && (
              <div className="mt-6">
                <h4 className="font-semibold mb-3">سجل القياسات</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {weightEntries.slice(-5).reverse().map((entry) => (
                    <div key={entry.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <div>
                        <span className="font-medium">{entry.weight} كجم</span>
                        <span className="text-sm text-gray-600 mr-2">
                          (زيادة: {(entry.weight - parseFloat(prePregnancyWeight)).toFixed(1)} كجم)
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">
                        الأسبوع {entry.week} - {entry.date.toLocaleDateString('ar-SA')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
