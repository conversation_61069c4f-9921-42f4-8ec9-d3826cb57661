/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQUhNRURBUUwlNUNEZXNrdG9wJTVDJUQ4JUFEJUQ4JUE3JUQ4JUIzJUQ4JUE4JUQ4JUE5JTVDc3JjJTVDYXBwJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlZ25hbmN5LWNhbGN1bGF0b3IvP2MyZDEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBSE1FREFRTFxcXFxEZXNrdG9wXFxcXNit2KfYs9io2KlcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PregnancyForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PregnancyForm */ \"(ssr)/./src/components/PregnancyForm.tsx\");\n/* harmony import */ var _components_PregnancyResults__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/PregnancyResults */ \"(ssr)/./src/components/PregnancyResults.tsx\");\n/* harmony import */ var _utils_pregnancyCalculations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/pregnancyCalculations */ \"(ssr)/./src/utils/pregnancyCalculations.ts\");\n/* harmony import */ var _barrel_optimize_names_Baby_Heart_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Heart,Shield,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/baby.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Heart_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Heart,Shield,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Heart_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Heart,Shield,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_Heart_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,Heart,Shield,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResults, setShowResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleFormSubmit = (data)=>{\n        const calculatedResults = (0,_utils_pregnancyCalculations__WEBPACK_IMPORTED_MODULE_4__.calculatePregnancy)(data);\n        setResults(calculatedResults);\n        setShowResults(true);\n    };\n    const handleBack = ()=>{\n        setShowResults(false);\n        setResults(null);\n    };\n    if (showResults && results) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            className: \"min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 py-8 px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PregnancyResults__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                results: results,\n                onBack: handleBack\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-pink-50 to-purple-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center w-20 h-20 bg-primary-100 rounded-full mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Heart_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-10 h-10 text-primary-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n                            children: \"حاسبة الحمل المتقدمة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto mb-8\",\n                            children: \"احسبي مدة حملك وتاريخ الولادة المتوقع بدقة مع دعم التقويم الهجري والميلادي\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Heart_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-8 h-8 text-red-500 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"حسابات دقيقة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"حساب مدة الحمل وتاريخ الولادة بناءً على آخر دورة شهرية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Heart_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-8 h-8 text-yellow-500 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"نصائح أسبوعية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"نصائح صحية مخصصة لكل أسبوع من أسابيع الحمل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_Heart_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-8 h-8 text-green-500 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"التقويم المزدوج\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"دعم التقويم الهجري والميلادي لراحتك\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PregnancyForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    onSubmit: handleFormSubmit\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-yellow-800 mb-2\",\n                                children: \"تنويه مهم\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-700 leading-relaxed\",\n                                children: \"هذه الحاسبة تقدم تقديرات تقريبية بناءً على المعلومات المدخلة. النتائج قد تختلف من امرأة لأخرى. يرجى استشارة طبيب النساء والولادة للحصول على متابعة طبية دقيقة ومعلومات موثوقة حول حملك. لا تعتبر هذه الحاسبة بديلاً عن الاستشارة الطبية المتخصصة.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"text-center mt-16 py-8 border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"\\xa9 2024 حاسبة الحمل المتقدمة - جميع الحقوق محفوظة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 mt-2\",\n                            children: \"تم تطويره بعناية لخدمة الأمهات في العالم العربي\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PregnancyForm.tsx":
/*!******************************************!*\
  !*** ./src/components/PregnancyForm.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PregnancyForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PregnancyForm({ onSubmit }) {\n    const [lastPeriodDate, setLastPeriodDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [cycleLength, setCycleLength] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(28);\n    const [menstrualLength, setMenstrualLength] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!lastPeriodDate) {\n            alert(\"يرجى إدخال تاريخ آخر دورة شهرية\");\n            return;\n        }\n        const date = new Date(lastPeriodDate);\n        if (isNaN(date.getTime())) {\n            alert(\"يرجى إدخال تاريخ صحيح\");\n            return;\n        }\n        onSubmit({\n            lastMenstrualPeriod: date,\n            cycleLength,\n            menstrualLength\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-8 h-8 text-primary-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"حاسبة الحمل\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"أدخلي بياناتك لحساب مدة الحمل وتاريخ الولادة المتوقع\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"inline w-4 h-4 ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"تاريخ أول يوم من آخر دورة شهرية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: lastPeriodDate,\n                                onChange: (e)=>setLastPeriodDate(e.target.value),\n                                className: \"input-field\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"inline w-4 h-4 ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"مدة الدورة الشهرية (بالأيام)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: cycleLength,\n                                onChange: (e)=>setCycleLength(Number(e.target.value)),\n                                className: \"input-field\",\n                                children: Array.from({\n                                    length: 21\n                                }, (_, i)=>i + 21).map((days)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: days,\n                                        children: [\n                                            days,\n                                            \" يوم\"\n                                        ]\n                                    }, days, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-1\",\n                                children: \"المدة الطبيعية: 21-35 يوم (المتوسط 28 يوم)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"مدة الطمث (اختياري)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: menstrualLength,\n                                onChange: (e)=>setMenstrualLength(Number(e.target.value)),\n                                className: \"input-field\",\n                                children: Array.from({\n                                    length: 8\n                                }, (_, i)=>i + 3).map((days)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: days,\n                                        children: [\n                                            days,\n                                            \" أيام\"\n                                        ]\n                                    }, days, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-1\",\n                                children: \"المدة الطبيعية: 3-7 أيام\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"btn-primary w-full text-lg py-3\",\n                        children: \"احسبي حملك\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-blue-50 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-blue-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"ملاحظة:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        \" هذه الحاسبة تعطي تقديرات تقريبية. يرجى استشارة طبيبك للحصول على معلومات دقيقة.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyForm.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PregnancyForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PregnancyResults.tsx":
/*!*********************************************!*\
  !*** ./src/components/PregnancyResults.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PregnancyResults)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Baby,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Baby,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Baby,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/baby.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Baby,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Baby,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Baby,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/hijriCalendar */ \"(ssr)/./src/utils/hijriCalendar.ts\");\n/* harmony import */ var _utils_pregnancyCalculations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pregnancyCalculations */ \"(ssr)/./src/utils/pregnancyCalculations.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction PregnancyResults({ results, onBack }) {\n    const [calendarType, setCalendarType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"gregorian\");\n    const hijriDueDate = (0,_utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__.gregorianToHijri)(results.dueDate);\n    const hijriOvulationDate = (0,_utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__.gregorianToHijri)(results.ovulationDate);\n    const trimesterInfo = (0,_utils_pregnancyCalculations__WEBPACK_IMPORTED_MODULE_3__.getTrimesterInfo)(results.trimester);\n    const weeklyTip = (0,_utils_pregnancyCalculations__WEBPACK_IMPORTED_MODULE_3__.getWeeklyTips)(results.currentWeeks);\n    const formatDate = (date)=>{\n        return calendarType === \"hijri\" ? (0,_utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__.formatHijriDate)((0,_utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__.gregorianToHijri)(date)) : (0,_utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__.formatGregorianDate)(date);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onBack,\n                                className: \"flex items-center text-primary-600 hover:text-primary-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-4 h-4 ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"حساب جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: calendarType,\n                                        onChange: (e)=>setCalendarType(e.target.value),\n                                        className: \"text-sm border rounded px-2 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"gregorian\",\n                                                children: \"ميلادي\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"hijri\",\n                                                children: \"هجري\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"نتائج حاسبة الحمل\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center bg-primary-100 text-primary-800 px-4 py-2 rounded-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: [\n                                            results.currentWeeks,\n                                            \" أسبوع و \",\n                                            results.currentDays,\n                                            \" أيام\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-5 h-5 ml-2 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            \"تقدم الحمل\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-200 rounded-full h-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-primary-400 to-primary-600 h-4 rounded-full transition-all duration-500\",\n                                    style: {\n                                        width: `${results.progressPercentage}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-gray-600 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"البداية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: [\n                                            results.progressPercentage.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"الولادة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5 ml-2 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"التواريخ المهمة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center p-3 bg-green-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"تاريخ الإباضة المتوقع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-700 font-semibold\",\n                                                children: formatDate(results.ovulationDate)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center p-3 bg-blue-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"تاريخ الولادة المتوقع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-700 font-semibold\",\n                                                children: formatDate(results.dueDate)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center p-3 bg-purple-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"الأيام المتبقية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-purple-700 font-semibold\",\n                                                children: [\n                                                    results.daysUntilDue,\n                                                    \" يوم\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 ml-2 text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"معلومات الثلث الحالي\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-4 bg-orange-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-bold text-orange-800 mb-2\",\n                                                children: trimesterInfo.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-orange-700 mb-2\",\n                                                children: trimesterInfo.weeks\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-orange-600\",\n                                                children: trimesterInfo.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"عمر الجنين:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-900 font-semibold mr-2\",\n                                                children: [\n                                                    Math.floor(results.fetalAge / 7),\n                                                    \" أسبوع و \",\n                                                    results.fetalAge % 7,\n                                                    \" أيام\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Baby_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-5 h-5 ml-2 text-pink-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            \"نصائح الأسبوع \",\n                            results.currentWeeks\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-pink-50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-pink-800\",\n                            children: weeklyTip\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"نصائح صحية عامة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-green-700\",\n                                        children: \"ما يجب فعله:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm space-y-1 text-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• تناول الفيتامينات المخصصة للحمل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• شرب الكثير من الماء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• ممارسة الرياضة الخفيفة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• الحصول على قسط كافٍ من النوم\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• المتابعة الدورية مع الطبيب\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-red-700\",\n                                        children: \"ما يجب تجنبه:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm space-y-1 text-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• التدخين والكحول\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• الكافيين المفرط\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• الأطعمة النيئة أو غير المطبوخة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• الأدوية بدون استشارة طبية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• التوتر والضغط النفسي\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PregnancyResults.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/hijriCalendar.ts":
/*!************************************!*\
  !*** ./src/utils/hijriCalendar.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatGregorianDate: () => (/* binding */ formatGregorianDate),\n/* harmony export */   formatHijriDate: () => (/* binding */ formatHijriDate),\n/* harmony export */   gregorianToHijri: () => (/* binding */ gregorianToHijri),\n/* harmony export */   hijriToGregorian: () => (/* binding */ hijriToGregorian)\n/* harmony export */ });\n// مساعد للتحويل بين التقويم الميلادي والهجري\nconst hijriMonths = [\n    \"محرم\",\n    \"صفر\",\n    \"ربيع الأول\",\n    \"ربيع الثاني\",\n    \"جمادى الأولى\",\n    \"جمادى الثانية\",\n    \"رجب\",\n    \"شعبان\",\n    \"رمضان\",\n    \"شوال\",\n    \"ذو القعدة\",\n    \"ذو الحجة\"\n];\n// تحويل تقريبي من الميلادي إلى الهجري\nfunction gregorianToHijri(gregorianDate) {\n    // هذا تحويل تقريبي - في التطبيق الحقيقي يفضل استخدام مكتبة متخصصة\n    const gregorianYear = gregorianDate.getFullYear();\n    const gregorianMonth = gregorianDate.getMonth() + 1;\n    const gregorianDay = gregorianDate.getDate();\n    // حساب تقريبي للسنة الهجرية\n    const hijriYear = Math.floor((gregorianYear - 622) * 1.030684);\n    // حساب تقريبي للشهر واليوم\n    const dayOfYear = getDayOfYear(gregorianDate);\n    const hijriDayOfYear = Math.floor(dayOfYear * 354 / 365);\n    const hijriMonth = Math.floor(hijriDayOfYear / 30) + 1;\n    const hijriDay = hijriDayOfYear % 30 + 1;\n    return {\n        year: hijriYear,\n        month: Math.min(hijriMonth, 12),\n        day: Math.min(hijriDay, 30),\n        monthName: hijriMonths[Math.min(hijriMonth - 1, 11)]\n    };\n}\n// تحويل تقريبي من الهجري إلى الميلادي\nfunction hijriToGregorian(hijriYear, hijriMonth, hijriDay) {\n    // حساب تقريبي للسنة الميلادية\n    const gregorianYear = Math.floor(hijriYear / 1.030684) + 622;\n    // حساب تقريبي لليوم في السنة\n    const hijriDayOfYear = (hijriMonth - 1) * 30 + hijriDay;\n    const gregorianDayOfYear = Math.floor(hijriDayOfYear * 365 / 354);\n    // إنشاء التاريخ الميلادي\n    const date = new Date(gregorianYear, 0, 1);\n    date.setDate(date.getDate() + gregorianDayOfYear - 1);\n    return date;\n}\nfunction getDayOfYear(date) {\n    const start = new Date(date.getFullYear(), 0, 0);\n    const diff = date.getTime() - start.getTime();\n    return Math.floor(diff / (1000 * 60 * 60 * 24));\n}\nfunction formatHijriDate(hijriDate) {\n    return `${hijriDate.day} ${hijriDate.monthName} ${hijriDate.year} هـ`;\n}\nfunction formatGregorianDate(date) {\n    const months = [\n        \"يناير\",\n        \"فبراير\",\n        \"مارس\",\n        \"أبريل\",\n        \"مايو\",\n        \"يونيو\",\n        \"يوليو\",\n        \"أغسطس\",\n        \"سبتمبر\",\n        \"أكتوبر\",\n        \"نوفمبر\",\n        \"ديسمبر\"\n    ];\n    return `${date.getDate()} ${months[date.getMonth()]} ${date.getFullYear()} م`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/hijriCalendar.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/pregnancyCalculations.ts":
/*!********************************************!*\
  !*** ./src/utils/pregnancyCalculations.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculatePregnancy: () => (/* binding */ calculatePregnancy),\n/* harmony export */   formatDateArabic: () => (/* binding */ formatDateArabic),\n/* harmony export */   getTrimesterInfo: () => (/* binding */ getTrimesterInfo),\n/* harmony export */   getWeeklyTips: () => (/* binding */ getWeeklyTips)\n/* harmony export */ });\n/* harmony import */ var date_fns_addDays__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! date-fns/addDays */ \"(ssr)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var date_fns_differenceInDays__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! date-fns/differenceInDays */ \"(ssr)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n/* harmony import */ var date_fns_format__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! date-fns/format */ \"(ssr)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! date-fns/locale */ \"(ssr)/./node_modules/date-fns/esm/locale/ar/index.js\");\n\n\n\n\nfunction calculatePregnancy(data) {\n    const { lastMenstrualPeriod, cycleLength } = data;\n    const today = new Date();\n    // حساب تاريخ الإباضة (عادة 14 يوم قبل نهاية الدورة)\n    const ovulationDate = (0,date_fns_addDays__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(lastMenstrualPeriod, cycleLength - 14);\n    // حساب تاريخ الولادة المتوقع (280 يوم من آخر دورة شهرية)\n    const dueDate = (0,date_fns_addDays__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(lastMenstrualPeriod, 280);\n    // حساب مدة الحمل الحالية\n    const totalDays = (0,date_fns_differenceInDays__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(today, lastMenstrualPeriod);\n    const currentWeeks = Math.floor(totalDays / 7);\n    const currentDays = totalDays % 7;\n    // حساب عمر الجنين (من تاريخ الإباضة)\n    const fetalAge = Math.max(0, (0,date_fns_differenceInDays__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(today, ovulationDate));\n    // تحديد الثلث الحالي\n    let trimester;\n    if (currentWeeks < 13) {\n        trimester = 1;\n    } else if (currentWeeks < 27) {\n        trimester = 2;\n    } else {\n        trimester = 3;\n    }\n    // حساب الأيام المتبقية للولادة\n    const daysUntilDue = Math.max(0, (0,date_fns_differenceInDays__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(dueDate, today));\n    // حساب نسبة التقدم\n    const progressPercentage = Math.min(100, totalDays / 280 * 100);\n    return {\n        currentWeeks,\n        currentDays,\n        ovulationDate,\n        dueDate,\n        fetalAge,\n        trimester,\n        daysUntilDue,\n        progressPercentage\n    };\n}\nfunction formatDateArabic(date) {\n    return (0,date_fns_format__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(date, \"dd MMMM yyyy\", {\n        locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    });\n}\nfunction getWeeklyTips(week) {\n    const tips = {\n        4: \"تهانينا! قد تبدأ أعراض الحمل في الظهور الآن.\",\n        8: \"الجنين بحجم حبة التوت. تجنبي الكافيين والتدخين.\",\n        12: \"انتهى الثلث الأول! قد تقل أعراض الغثيان.\",\n        16: \"قد تشعرين بحركة الجنين قريباً. تناولي الكالسيوم.\",\n        20: \"منتصف الحمل! وقت فحص الموجات فوق الصوتية المفصل.\",\n        24: \"الجنين يمكنه سماع صوتك الآن. تحدثي معه!\",\n        28: \"بداية الثلث الأخير. راقبي حركة الجنين يومياً.\",\n        32: \"الجنين ينمو بسرعة. احرصي على الراحة.\",\n        36: \"الجنين مكتمل النمو تقريباً. استعدي للولادة.\",\n        40: \"موعد الولادة المتوقع! كوني مستعدة في أي وقت.\"\n    };\n    // العثور على أقرب أسبوع متاح\n    const availableWeeks = Object.keys(tips).map(Number).sort((a, b)=>a - b);\n    const closestWeek = availableWeeks.reduce((prev, curr)=>Math.abs(curr - week) < Math.abs(prev - week) ? curr : prev);\n    return tips[closestWeek] || \"استمري في متابعة حملك مع طبيبك المختص.\";\n}\nfunction getTrimesterInfo(trimester) {\n    const info = {\n        1: {\n            name: \"الثلث الأول\",\n            description: \"فترة تكوين الأعضاء الأساسية للجنين\",\n            weeks: \"الأسابيع 1-12\"\n        },\n        2: {\n            name: \"الثلث الثاني\",\n            description: \"فترة النمو السريع وتطور الحواس\",\n            weeks: \"الأسابيع 13-26\"\n        },\n        3: {\n            name: \"الثلث الثالث\",\n            description: \"فترة اكتمال النمو والاستعداد للولادة\",\n            weeks: \"الأسابيع 27-40\"\n        }\n    };\n    return info[trimester];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvcHJlZ25hbmN5Q2FsY3VsYXRpb25zLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQWdGO0FBQUE7QUFBQTtBQUMzQztBQW1COUIsU0FBU0ksbUJBQW1CQyxJQUFtQjtJQUNwRCxNQUFNLEVBQUVDLG1CQUFtQixFQUFFQyxXQUFXLEVBQUUsR0FBR0Y7SUFDN0MsTUFBTUcsUUFBUSxJQUFJQztJQUVsQixvREFBb0Q7SUFDcEQsTUFBTUMsZ0JBQWdCViw0REFBT0EsQ0FBQ00scUJBQXFCQyxjQUFjO0lBRWpFLHlEQUF5RDtJQUN6RCxNQUFNSSxVQUFVWCw0REFBT0EsQ0FBQ00scUJBQXFCO0lBRTdDLHlCQUF5QjtJQUN6QixNQUFNTSxZQUFZWCxxRUFBZ0JBLENBQUNPLE9BQU9GO0lBQzFDLE1BQU1PLGVBQWVDLEtBQUtDLEtBQUssQ0FBQ0gsWUFBWTtJQUM1QyxNQUFNSSxjQUFjSixZQUFZO0lBRWhDLHFDQUFxQztJQUNyQyxNQUFNSyxXQUFXSCxLQUFLSSxHQUFHLENBQUMsR0FBR2pCLHFFQUFnQkEsQ0FBQ08sT0FBT0U7SUFFckQscUJBQXFCO0lBQ3JCLElBQUlTO0lBQ0osSUFBSU4sZUFBZSxJQUFJO1FBQ3JCTSxZQUFZO0lBQ2QsT0FBTyxJQUFJTixlQUFlLElBQUk7UUFDNUJNLFlBQVk7SUFDZCxPQUFPO1FBQ0xBLFlBQVk7SUFDZDtJQUVBLCtCQUErQjtJQUMvQixNQUFNQyxlQUFlTixLQUFLSSxHQUFHLENBQUMsR0FBR2pCLHFFQUFnQkEsQ0FBQ1UsU0FBU0g7SUFFM0QsbUJBQW1CO0lBQ25CLE1BQU1hLHFCQUFxQlAsS0FBS1EsR0FBRyxDQUFDLEtBQUssWUFBYSxNQUFPO0lBRTdELE9BQU87UUFDTFQ7UUFDQUc7UUFDQU47UUFDQUM7UUFDQU07UUFDQUU7UUFDQUM7UUFDQUM7SUFDRjtBQUNGO0FBRU8sU0FBU0UsaUJBQWlCQyxJQUFVO0lBQ3pDLE9BQU90QiwyREFBTUEsQ0FBQ3NCLE1BQU0sZ0JBQWdCO1FBQUVDLFFBQVF0Qix1REFBRUE7SUFBQztBQUNuRDtBQUVPLFNBQVN1QixjQUFjQyxJQUFZO0lBQ3hDLE1BQU1DLE9BQWtDO1FBQ3RDLEdBQUc7UUFDSCxHQUFHO1FBQ0gsSUFBSTtRQUNKLElBQUk7UUFDSixJQUFJO1FBQ0osSUFBSTtRQUNKLElBQUk7UUFDSixJQUFJO1FBQ0osSUFBSTtRQUNKLElBQUk7SUFDTjtJQUVBLDZCQUE2QjtJQUM3QixNQUFNQyxpQkFBaUJDLE9BQU9DLElBQUksQ0FBQ0gsTUFBTUksR0FBRyxDQUFDQyxRQUFRQyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsSUFBSUM7SUFDeEUsTUFBTUMsY0FBY1IsZUFBZVMsTUFBTSxDQUFDLENBQUNDLE1BQU1DLE9BQy9DMUIsS0FBSzJCLEdBQUcsQ0FBQ0QsT0FBT2IsUUFBUWIsS0FBSzJCLEdBQUcsQ0FBQ0YsT0FBT1osUUFBUWEsT0FBT0Q7SUFHekQsT0FBT1gsSUFBSSxDQUFDUyxZQUFZLElBQUk7QUFDOUI7QUFFTyxTQUFTSyxpQkFBaUJ2QixTQUFvQjtJQUNuRCxNQUFNd0IsT0FBTztRQUNYLEdBQUc7WUFDREMsTUFBTTtZQUNOQyxhQUFhO1lBQ2JDLE9BQU87UUFDVDtRQUNBLEdBQUc7WUFDREYsTUFBTTtZQUNOQyxhQUFhO1lBQ2JDLE9BQU87UUFDVDtRQUNBLEdBQUc7WUFDREYsTUFBTTtZQUNOQyxhQUFhO1lBQ2JDLE9BQU87UUFDVDtJQUNGO0lBRUEsT0FBT0gsSUFBSSxDQUFDeEIsVUFBVTtBQUN4QiIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZWduYW5jeS1jYWxjdWxhdG9yLy4vc3JjL3V0aWxzL3ByZWduYW5jeUNhbGN1bGF0aW9ucy50cz8yZDY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFkZERheXMsIGRpZmZlcmVuY2VJbkRheXMsIGRpZmZlcmVuY2VJbldlZWtzLCBmb3JtYXQgfSBmcm9tICdkYXRlLWZucyc7XG5pbXBvcnQgeyBhciB9IGZyb20gJ2RhdGUtZm5zL2xvY2FsZSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgUHJlZ25hbmN5RGF0YSB7XG4gIGxhc3RNZW5zdHJ1YWxQZXJpb2Q6IERhdGU7XG4gIGN5Y2xlTGVuZ3RoOiBudW1iZXI7XG4gIG1lbnN0cnVhbExlbmd0aD86IG51bWJlcjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBQcmVnbmFuY3lSZXN1bHRzIHtcbiAgY3VycmVudFdlZWtzOiBudW1iZXI7XG4gIGN1cnJlbnREYXlzOiBudW1iZXI7XG4gIG92dWxhdGlvbkRhdGU6IERhdGU7XG4gIGR1ZURhdGU6IERhdGU7XG4gIGZldGFsQWdlOiBudW1iZXI7XG4gIHRyaW1lc3RlcjogMSB8IDIgfCAzO1xuICBkYXlzVW50aWxEdWU6IG51bWJlcjtcbiAgcHJvZ3Jlc3NQZXJjZW50YWdlOiBudW1iZXI7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjYWxjdWxhdGVQcmVnbmFuY3koZGF0YTogUHJlZ25hbmN5RGF0YSk6IFByZWduYW5jeVJlc3VsdHMge1xuICBjb25zdCB7IGxhc3RNZW5zdHJ1YWxQZXJpb2QsIGN5Y2xlTGVuZ3RoIH0gPSBkYXRhO1xuICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7XG4gIFxuICAvLyDYrdiz2KfYqCDYqtin2LHZitiuINin2YTYpdio2KfYttipICjYudin2K/YqSAxNCDZitmI2YUg2YLYqNmEINmG2YfYp9mK2Kkg2KfZhNiv2YjYsdipKVxuICBjb25zdCBvdnVsYXRpb25EYXRlID0gYWRkRGF5cyhsYXN0TWVuc3RydWFsUGVyaW9kLCBjeWNsZUxlbmd0aCAtIDE0KTtcbiAgXG4gIC8vINit2LPYp9ioINiq2KfYsdmK2K4g2KfZhNmI2YTYp9iv2Kkg2KfZhNmF2KrZiNmC2LkgKDI4MCDZitmI2YUg2YXZhiDYotiu2LEg2K/ZiNix2Kkg2LTZh9ix2YrYqSlcbiAgY29uc3QgZHVlRGF0ZSA9IGFkZERheXMobGFzdE1lbnN0cnVhbFBlcmlvZCwgMjgwKTtcbiAgXG4gIC8vINit2LPYp9ioINmF2K/YqSDYp9mE2K3ZhdmEINin2YTYrdin2YTZitipXG4gIGNvbnN0IHRvdGFsRGF5cyA9IGRpZmZlcmVuY2VJbkRheXModG9kYXksIGxhc3RNZW5zdHJ1YWxQZXJpb2QpO1xuICBjb25zdCBjdXJyZW50V2Vla3MgPSBNYXRoLmZsb29yKHRvdGFsRGF5cyAvIDcpO1xuICBjb25zdCBjdXJyZW50RGF5cyA9IHRvdGFsRGF5cyAlIDc7XG4gIFxuICAvLyDYrdiz2KfYqCDYudmF2LEg2KfZhNis2YbZitmGICjZhdmGINiq2KfYsdmK2K4g2KfZhNil2KjYp9i22KkpXG4gIGNvbnN0IGZldGFsQWdlID0gTWF0aC5tYXgoMCwgZGlmZmVyZW5jZUluRGF5cyh0b2RheSwgb3Z1bGF0aW9uRGF0ZSkpO1xuICBcbiAgLy8g2KrYrdiv2YrYryDYp9mE2KvZhNirINin2YTYrdin2YTZilxuICBsZXQgdHJpbWVzdGVyOiAxIHwgMiB8IDM7XG4gIGlmIChjdXJyZW50V2Vla3MgPCAxMykge1xuICAgIHRyaW1lc3RlciA9IDE7XG4gIH0gZWxzZSBpZiAoY3VycmVudFdlZWtzIDwgMjcpIHtcbiAgICB0cmltZXN0ZXIgPSAyO1xuICB9IGVsc2Uge1xuICAgIHRyaW1lc3RlciA9IDM7XG4gIH1cbiAgXG4gIC8vINit2LPYp9ioINin2YTYo9mK2KfZhSDYp9mE2YXYqtio2YLZitipINmE2YTZiNmE2KfYr9ipXG4gIGNvbnN0IGRheXNVbnRpbER1ZSA9IE1hdGgubWF4KDAsIGRpZmZlcmVuY2VJbkRheXMoZHVlRGF0ZSwgdG9kYXkpKTtcbiAgXG4gIC8vINit2LPYp9ioINmG2LPYqNipINin2YTYqtmC2K/ZhVxuICBjb25zdCBwcm9ncmVzc1BlcmNlbnRhZ2UgPSBNYXRoLm1pbigxMDAsICh0b3RhbERheXMgLyAyODApICogMTAwKTtcbiAgXG4gIHJldHVybiB7XG4gICAgY3VycmVudFdlZWtzLFxuICAgIGN1cnJlbnREYXlzLFxuICAgIG92dWxhdGlvbkRhdGUsXG4gICAgZHVlRGF0ZSxcbiAgICBmZXRhbEFnZSxcbiAgICB0cmltZXN0ZXIsXG4gICAgZGF5c1VudGlsRHVlLFxuICAgIHByb2dyZXNzUGVyY2VudGFnZVxuICB9O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0RGF0ZUFyYWJpYyhkYXRlOiBEYXRlKTogc3RyaW5nIHtcbiAgcmV0dXJuIGZvcm1hdChkYXRlLCAnZGQgTU1NTSB5eXl5JywgeyBsb2NhbGU6IGFyIH0pO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0V2Vla2x5VGlwcyh3ZWVrOiBudW1iZXIpOiBzdHJpbmcge1xuICBjb25zdCB0aXBzOiB7IFtrZXk6IG51bWJlcl06IHN0cmluZyB9ID0ge1xuICAgIDQ6IFwi2KrZh9in2YbZitmG2KchINmC2K8g2KrYqNiv2KMg2KPYudix2KfYtiDYp9mE2K3ZhdmEINmB2Yog2KfZhNi42YfZiNixINin2YTYotmGLlwiLFxuICAgIDg6IFwi2KfZhNis2YbZitmGINio2K3YrNmFINit2KjYqSDYp9mE2KrZiNiqLiDYqtis2YbYqNmKINin2YTZg9in2YHZitmK2YYg2YjYp9mE2KrYr9iu2YrZhi5cIixcbiAgICAxMjogXCLYp9mG2KrZh9mJINin2YTYq9mE2Ksg2KfZhNij2YjZhCEg2YLYryDYqtmC2YQg2KPYudix2KfYtiDYp9mE2LrYq9mK2KfZhi5cIixcbiAgICAxNjogXCLZgtivINiq2LTYudix2YrZhiDYqNit2LHZg9ipINin2YTYrNmG2YrZhiDZgtix2YrYqNin2YsuINiq2YbYp9mI2YTZiiDYp9mE2YPYp9mE2LPZitmI2YUuXCIsXG4gICAgMjA6IFwi2YXZhtiq2LXZgSDYp9mE2K3ZhdmEISDZiNmC2Kog2YHYrdi1INin2YTZhdmI2KzYp9iqINmB2YjZgiDYp9mE2LXZiNiq2YrYqSDYp9mE2YXZgdi12YQuXCIsXG4gICAgMjQ6IFwi2KfZhNis2YbZitmGINmK2YXZg9mG2Ycg2LPZhdin2Lkg2LXZiNiq2YMg2KfZhNii2YYuINiq2K3Yr9ir2Yog2YXYudmHIVwiLFxuICAgIDI4OiBcItio2K/Yp9mK2Kkg2KfZhNir2YTYqyDYp9mE2KPYrtmK2LEuINix2KfZgtio2Yog2K3YsdmD2Kkg2KfZhNis2YbZitmGINmK2YjZhdmK2KfZiy5cIixcbiAgICAzMjogXCLYp9mE2KzZhtmK2YYg2YrZhtmF2Ygg2KjYs9ix2LnYqS4g2KfYrdix2LXZiiDYudmE2Ykg2KfZhNix2KfYrdipLlwiLFxuICAgIDM2OiBcItin2YTYrNmG2YrZhiDZhdmD2KrZhdmEINin2YTZhtmF2Ygg2KrZgtix2YrYqNin2YsuINin2LPYqti52K/ZiiDZhNmE2YjZhNin2K/YqS5cIixcbiAgICA0MDogXCLZhdmI2LnYryDYp9mE2YjZhNin2K/YqSDYp9mE2YXYqtmI2YLYuSEg2YPZiNmG2Yog2YXYs9iq2LnYr9ipINmB2Yog2KPZiiDZiNmC2KouXCJcbiAgfTtcbiAgXG4gIC8vINin2YTYudir2YjYsSDYudmE2Ykg2KPZgtix2Kgg2KPYs9io2YjYuSDZhdiq2KfYrVxuICBjb25zdCBhdmFpbGFibGVXZWVrcyA9IE9iamVjdC5rZXlzKHRpcHMpLm1hcChOdW1iZXIpLnNvcnQoKGEsIGIpID0+IGEgLSBiKTtcbiAgY29uc3QgY2xvc2VzdFdlZWsgPSBhdmFpbGFibGVXZWVrcy5yZWR1Y2UoKHByZXYsIGN1cnIpID0+IFxuICAgIE1hdGguYWJzKGN1cnIgLSB3ZWVrKSA8IE1hdGguYWJzKHByZXYgLSB3ZWVrKSA/IGN1cnIgOiBwcmV2XG4gICk7XG4gIFxuICByZXR1cm4gdGlwc1tjbG9zZXN0V2Vla10gfHwgXCLYp9iz2KrZhdix2Yog2YHZiiDZhdiq2KfYqNi52Kkg2K3ZhdmE2YMg2YXYuSDYt9io2YrYqNmDINin2YTZhdiu2KrYtS5cIjtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFRyaW1lc3RlckluZm8odHJpbWVzdGVyOiAxIHwgMiB8IDMpOiB7IG5hbWU6IHN0cmluZzsgZGVzY3JpcHRpb246IHN0cmluZzsgd2Vla3M6IHN0cmluZyB9IHtcbiAgY29uc3QgaW5mbyA9IHtcbiAgICAxOiB7XG4gICAgICBuYW1lOiBcItin2YTYq9mE2Ksg2KfZhNij2YjZhFwiLFxuICAgICAgZGVzY3JpcHRpb246IFwi2YHYqtix2Kkg2KrZg9mI2YrZhiDYp9mE2KPYudi22KfYoSDYp9mE2KPYs9in2LPZitipINmE2YTYrNmG2YrZhlwiLFxuICAgICAgd2Vla3M6IFwi2KfZhNij2LPYp9io2YrYuSAxLTEyXCJcbiAgICB9LFxuICAgIDI6IHtcbiAgICAgIG5hbWU6IFwi2KfZhNir2YTYqyDYp9mE2KvYp9mG2YpcIiwgXG4gICAgICBkZXNjcmlwdGlvbjogXCLZgdiq2LHYqSDYp9mE2YbZhdmIINin2YTYs9ix2YrYuSDZiNiq2LfZiNixINin2YTYrdmI2KfYs1wiLFxuICAgICAgd2Vla3M6IFwi2KfZhNij2LPYp9io2YrYuSAxMy0yNlwiXG4gICAgfSxcbiAgICAzOiB7XG4gICAgICBuYW1lOiBcItin2YTYq9mE2Ksg2KfZhNir2KfZhNirXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCLZgdiq2LHYqSDYp9mD2KrZhdin2YQg2KfZhNmG2YXZiCDZiNin2YTYp9iz2KrYudiv2KfYryDZhNmE2YjZhNin2K/YqVwiLFxuICAgICAgd2Vla3M6IFwi2KfZhNij2LPYp9io2YrYuSAyNy00MFwiXG4gICAgfVxuICB9O1xuICBcbiAgcmV0dXJuIGluZm9bdHJpbWVzdGVyXTtcbn1cbiJdLCJuYW1lcyI6WyJhZGREYXlzIiwiZGlmZmVyZW5jZUluRGF5cyIsImZvcm1hdCIsImFyIiwiY2FsY3VsYXRlUHJlZ25hbmN5IiwiZGF0YSIsImxhc3RNZW5zdHJ1YWxQZXJpb2QiLCJjeWNsZUxlbmd0aCIsInRvZGF5IiwiRGF0ZSIsIm92dWxhdGlvbkRhdGUiLCJkdWVEYXRlIiwidG90YWxEYXlzIiwiY3VycmVudFdlZWtzIiwiTWF0aCIsImZsb29yIiwiY3VycmVudERheXMiLCJmZXRhbEFnZSIsIm1heCIsInRyaW1lc3RlciIsImRheXNVbnRpbER1ZSIsInByb2dyZXNzUGVyY2VudGFnZSIsIm1pbiIsImZvcm1hdERhdGVBcmFiaWMiLCJkYXRlIiwibG9jYWxlIiwiZ2V0V2Vla2x5VGlwcyIsIndlZWsiLCJ0aXBzIiwiYXZhaWxhYmxlV2Vla3MiLCJPYmplY3QiLCJrZXlzIiwibWFwIiwiTnVtYmVyIiwic29ydCIsImEiLCJiIiwiY2xvc2VzdFdlZWsiLCJyZWR1Y2UiLCJwcmV2IiwiY3VyciIsImFicyIsImdldFRyaW1lc3RlckluZm8iLCJpbmZvIiwibmFtZSIsImRlc2NyaXB0aW9uIiwid2Vla3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/pregnancyCalculations.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4dabe7200398\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlZ25hbmN5LWNhbGN1bGF0b3IvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzQ0MzUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0ZGFiZTcyMDAzOThcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"حاسبة الحمل - Pregnancy Calculator\",\n    description: \"حاسبة الحمل المتقدمة لحساب مدة الحمل وتاريخ الولادة المتوقع\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-arabic\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxLQUFJO2tCQUNsQiw0RUFBQ0M7WUFBS0MsV0FBVTtzQkFDYkw7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVnbmFuY3ktY2FsY3VsYXRvci8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ9it2KfYs9io2Kkg2KfZhNit2YXZhCAtIFByZWduYW5jeSBDYWxjdWxhdG9yJyxcbiAgZGVzY3JpcHRpb246ICfYrdin2LPYqNipINin2YTYrdmF2YQg2KfZhNmF2KrZgtiv2YXYqSDZhNit2LPYp9ioINmF2K/YqSDYp9mE2K3ZhdmEINmI2KrYp9ix2YrYriDYp9mE2YjZhNin2K/YqSDYp9mE2YXYqtmI2YLYuScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1hcmFiaWNcIj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiZGlyIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\حاسبة\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/date-fns","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/@babel"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAHMEDAQL%5CDesktop%5C%D8%AD%D8%A7%D8%B3%D8%A8%D8%A9&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();