'use client';

import { useState } from 'react';
import { Calendar, Clock, Baby, Heart, Globe, ArrowLeft, BarChart3, Activity, Bell, Brain } from 'lucide-react';
import { PregnancyResults as Results } from '@/utils/pregnancyCalculations';
import { formatHijriDate, formatGregorianDate, gregorianToHijri } from '@/utils/hijriCalendar';
import { getWeeklyTips, getTrimesterInfo } from '@/utils/pregnancyCalculations';
import PregnancyCharts from './PregnancyCharts';
import SymptomTracker from './SymptomTracker';
import SmartReminders from './SmartReminders';
import HealthAnalytics from './HealthAnalytics';
import { motion } from 'framer-motion';

interface PregnancyResultsProps {
  results: Results;
  onBack: () => void;
  lastMenstrualPeriod?: Date;
}

export default function PregnancyResults({ results, onBack, lastMenstrualPeriod }: PregnancyResultsProps) {
  const [calendarType, setCalendarType] = useState<'gregorian' | 'hijri'>('gregorian');
  const [activeTab, setActiveTab] = useState<'overview' | 'charts' | 'symptoms' | 'reminders' | 'analytics'>('overview');

  const hijriDueDate = gregorianToHijri(results.dueDate);
  const hijriOvulationDate = gregorianToHijri(results.ovulationDate);
  const trimesterInfo = getTrimesterInfo(results.trimester);
  const weeklyTip = getWeeklyTips(results.currentWeeks);

  const formatDate = (date: Date) => {
    return calendarType === 'hijri' 
      ? formatHijriDate(gregorianToHijri(date))
      : formatGregorianDate(date);
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="card-gradient relative overflow-hidden">
        {/* خلفية زخرفية */}
        <div className="absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full -translate-y-20 translate-x-20"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/10 rounded-full translate-y-16 -translate-x-16"></div>

        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={onBack}
              className="flex items-center text-white/90 hover:text-white bg-white/20 backdrop-blur-sm px-4 py-2 rounded-xl transition-all duration-300 hover:bg-white/30"
            >
              <ArrowLeft className="w-4 h-4 ml-1" />
              حساب جديد
            </button>

            <div className="flex items-center space-x-3 space-x-reverse">
              <Globe className="w-5 h-5 text-white/80" />
              <select
                value={calendarType}
                onChange={(e) => setCalendarType(e.target.value as 'gregorian' | 'hijri')}
                className="bg-white/20 backdrop-blur-sm text-white border border-white/30 rounded-xl px-4 py-2 text-sm font-medium"
              >
                <option value="gregorian" className="text-gray-800">ميلادي</option>
                <option value="hijri" className="text-gray-800">هجري</option>
              </select>
            </div>
          </div>

          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-black text-white mb-6 leading-tight">
              نتائج حاسبة الحمل
            </h1>
            <div className="inline-flex items-center bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-2xl border border-white/30 floating-animation">
              <Baby className="w-8 h-8 ml-3" />
              <span className="text-2xl font-bold">
                {results.currentWeeks} أسبوع و {results.currentDays} أيام
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="card">
        <div className="flex space-x-1 space-x-reverse overflow-x-auto">
          <button
            onClick={() => setActiveTab('overview')}
            className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${
              activeTab === 'overview'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Heart className="w-4 h-4 inline ml-1" />
            نظرة عامة
          </button>
          <button
            onClick={() => setActiveTab('charts')}
            className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${
              activeTab === 'charts'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <BarChart3 className="w-4 h-4 inline ml-1" />
            الرسوم البيانية
          </button>
          <button
            onClick={() => setActiveTab('symptoms')}
            className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${
              activeTab === 'symptoms'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Activity className="w-4 h-4 inline ml-1" />
            تتبع الأعراض
          </button>
          <button
            onClick={() => setActiveTab('reminders')}
            className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${
              activeTab === 'reminders'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Bell className="w-4 h-4 inline ml-1" />
            التذكيرات
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${
              activeTab === 'analytics'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Brain className="w-4 h-4 inline ml-1" />
            التحليل الذكي
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'overview' && (
          <>
            {/* Progress Bar */}
      <div className="card relative overflow-hidden">
        <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full opacity-50 -translate-y-12 translate-x-12"></div>

        <div className="relative z-10">
          <h3 className="text-2xl font-bold mb-6 flex items-center">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-red-400 to-pink-500 flex items-center justify-center ml-3">
              <Heart className="w-6 h-6 text-white" />
            </div>
            تقدم الحمل
          </h3>
          <div className="relative mb-6">
            <div className="w-full bg-gray-200 rounded-2xl h-6 overflow-hidden">
              <div
                className="h-6 rounded-2xl transition-all duration-1000 ease-out relative overflow-hidden"
                style={{
                  width: `${results.progressPercentage}%`,
                  background: 'linear-gradient(90deg, #ec4899 0%, #8b5cf6 50%, #06b6d4 100%)'
                }}
              >
                <div className="absolute inset-0 bg-white/20 animate-pulse"></div>
              </div>
            </div>
            <div className="flex justify-between text-lg font-semibold text-gray-700 mt-4">
              <span className="bg-gray-100 px-3 py-1 rounded-lg">البداية</span>
              <span className="bg-gradient-to-r from-pink-500 to-purple-500 text-white px-4 py-1 rounded-lg">
                {results.progressPercentage.toFixed(1)}%
              </span>
              <span className="bg-gray-100 px-3 py-1 rounded-lg">الولادة</span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Key Dates */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Calendar className="w-5 h-5 ml-2 text-blue-500" />
            التواريخ المهمة
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
              <span className="font-medium">تاريخ الإباضة المتوقع</span>
              <span className="text-green-700 font-semibold">
                {formatDate(results.ovulationDate)}
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
              <span className="font-medium">تاريخ الولادة المتوقع</span>
              <span className="text-blue-700 font-semibold">
                {formatDate(results.dueDate)}
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
              <span className="font-medium">الأيام المتبقية</span>
              <span className="text-purple-700 font-semibold">
                {results.daysUntilDue} يوم
              </span>
            </div>
          </div>
        </div>

        {/* Trimester Info */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Clock className="w-5 h-5 ml-2 text-orange-500" />
            معلومات الثلث الحالي
          </h3>
          <div className="space-y-4">
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <h4 className="text-xl font-bold text-orange-800 mb-2">
                {trimesterInfo.name}
              </h4>
              <p className="text-orange-700 mb-2">{trimesterInfo.weeks}</p>
              <p className="text-sm text-orange-600">{trimesterInfo.description}</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">عمر الجنين:</span>
              <span className="text-gray-900 font-semibold mr-2">
                {Math.floor(results.fetalAge / 7)} أسبوع و {results.fetalAge % 7} أيام
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Weekly Tips */}
      <div className="card">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Heart className="w-5 h-5 ml-2 text-pink-500" />
          نصائح الأسبوع {results.currentWeeks}
        </h3>
        <div className="p-4 bg-pink-50 rounded-lg">
          <p className="text-pink-800">{weeklyTip}</p>
        </div>
      </div>

      {/* Health Tips */}
      <div className="card">
        <h3 className="text-lg font-semibold mb-4">نصائح صحية عامة</h3>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-green-700">ما يجب فعله:</h4>
            <ul className="text-sm space-y-1 text-gray-700">
              <li>• تناول الفيتامينات المخصصة للحمل</li>
              <li>• شرب الكثير من الماء</li>
              <li>• ممارسة الرياضة الخفيفة</li>
              <li>• الحصول على قسط كافٍ من النوم</li>
              <li>• المتابعة الدورية مع الطبيب</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-red-700">ما يجب تجنبه:</h4>
            <ul className="text-sm space-y-1 text-gray-700">
              <li>• التدخين والكحول</li>
              <li>• الكافيين المفرط</li>
              <li>• الأطعمة النيئة أو غير المطبوخة</li>
              <li>• الأدوية بدون استشارة طبية</li>
              <li>• التوتر والضغط النفسي</li>
            </ul>
          </div>
        </div>
      </div>
          </>
        )}

        {activeTab === 'charts' && (
          <PregnancyCharts results={results} />
        )}

        {activeTab === 'symptoms' && (
          <SymptomTracker pregnancyWeek={results.currentWeeks} />
        )}

        {activeTab === 'reminders' && lastMenstrualPeriod && (
          <SmartReminders
            pregnancyWeek={results.currentWeeks}
            lastMenstrualPeriod={lastMenstrualPeriod}
          />
        )}

        {activeTab === 'analytics' && lastMenstrualPeriod && (
          <HealthAnalytics
            results={results}
            lastMenstrualPeriod={lastMenstrualPeriod}
          />
        )}
      </motion.div>
    </div>
  );
}
