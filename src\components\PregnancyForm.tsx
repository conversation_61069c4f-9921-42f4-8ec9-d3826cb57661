'use client';

import { useState } from 'react';
import { Calendar, Clock, User } from 'lucide-react';

interface PregnancyFormProps {
  onSubmit: (data: {
    lastMenstrualPeriod: Date;
    cycleLength: number;
    menstrualLength?: number;
  }) => void;
}

export default function PregnancyForm({ onSubmit }: PregnancyFormProps) {
  const [lastPeriodDate, setLastPeriodDate] = useState('');
  const [cycleLength, setCycleLength] = useState(28);
  const [menstrualLength, setMenstrualLength] = useState(5);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!lastPeriodDate) {
      alert('يرجى إدخال تاريخ آخر دورة شهرية');
      return;
    }

    const date = new Date(lastPeriodDate);
    if (isNaN(date.getTime())) {
      alert('يرجى إدخال تاريخ صحيح');
      return;
    }

    onSubmit({
      lastMenstrualPeriod: date,
      cycleLength,
      menstrualLength
    });
  };

  return (
    <div className="card max-w-lg mx-auto relative overflow-hidden">
      {/* خلفية زخرفية */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-pink-200 to-purple-200 rounded-full opacity-20 -translate-y-16 translate-x-16"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-cyan-200 to-blue-200 rounded-full opacity-20 translate-y-12 -translate-x-12"></div>

      <div className="relative z-10">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 mb-6 floating-animation">
            <div className="w-full h-full rounded-2xl glass-effect flex items-center justify-center pulse-glow">
              <User className="w-10 h-10 text-pink-600" />
            </div>
          </div>
          <h2 className="text-3xl font-bold mb-3">
            <span className="gradient-text">حاسبة الحمل</span>
          </h2>
          <p className="text-gray-600 text-lg leading-relaxed">أدخلي بياناتك لحساب مدة الحمل وتاريخ الولادة المتوقع</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="space-y-2">
            <label className="block text-lg font-semibold text-gray-800 mb-3">
              <Calendar className="inline w-5 h-5 ml-2 text-pink-600" />
              تاريخ أول يوم من آخر دورة شهرية
            </label>
            <input
              type="date"
              value={lastPeriodDate}
              onChange={(e) => setLastPeriodDate(e.target.value)}
              className="input-field text-lg"
              required
            />
          </div>

          <div className="space-y-2">
            <label className="block text-lg font-semibold text-gray-800 mb-3">
              <Clock className="inline w-5 h-5 ml-2 text-purple-600" />
              مدة الدورة الشهرية (بالأيام)
            </label>
            <select
              value={cycleLength}
              onChange={(e) => setCycleLength(Number(e.target.value))}
              className="input-field text-lg"
            >
              {Array.from({ length: 21 }, (_, i) => i + 21).map(days => (
                <option key={days} value={days}>{days} يوم</option>
              ))}
            </select>
            <p className="text-sm text-gray-600 mt-2 bg-gray-50 p-3 rounded-lg">
              💡 المدة الطبيعية: 21-35 يوم (المتوسط 28 يوم)
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              مدة الطمث (اختياري)
            </label>
            <select
              value={menstrualLength}
              onChange={(e) => setMenstrualLength(Number(e.target.value))}
              className="input-field"
            >
              {Array.from({ length: 8 }, (_, i) => i + 3).map(days => (
                <option key={days} value={days}>{days} أيام</option>
              ))}
            </select>
            <p className="text-xs text-gray-500 mt-1">المدة الطبيعية: 3-7 أيام</p>
          </div>

          <button
            type="submit"
            className="btn-primary w-full text-xl py-4 font-bold shadow-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
          >
            ✨ احسبي حملك الآن
          </button>
        </form>

        <div className="mt-8 p-6 glass-effect rounded-2xl border border-blue-200/50">
          <div className="flex items-start space-x-3 space-x-reverse">
            <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
              <span className="text-blue-600 text-sm">💡</span>
            </div>
            <div>
              <p className="text-blue-800 font-medium mb-1">نصيحة مهمة</p>
              <p className="text-sm text-blue-700 leading-relaxed">
                للحصول على أدق النتائج، تأكدي من إدخال تاريخ أول يوم من آخر دورة شهرية بدقة.
                هذا التاريخ هو الأساس في حساب مدة الحمل وتاريخ الولادة المتوقع.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
