'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON>s, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, ResponsiveContaine<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Bar<PERSON>hart, Bar } from 'recharts';
import { TrendingUp, Baby, Calendar, Weight } from 'lucide-react';
import { PregnancyResults } from '@/utils/pregnancyCalculations';

interface PregnancyChartsProps {
  results: PregnancyResults;
}

export default function PregnancyCharts({ results }: PregnancyChartsProps) {
  // بيانات نمو الجنين حسب الأسابيع (بيانات طبية دقيقة)
  const fetalGrowthData = [
    { week: 4, length: 0.4, weight: 0.02, milestone: 'بداية تكوين القلب والأنبوب العصبي', size: 'بحجم بذرة الخشخاش' },
    { week: 6, length: 0.6, weight: 0.04, milestone: 'بداية نبضات القلب', size: 'بحجم حبة العدس' },
    { week: 8, length: 1.6, weight: 1, milestone: 'تكوين الأطراف والوجه', size: 'بحجم حبة التوت' },
    { week: 10, length: 3.1, weight: 4, milestone: 'تكوين الأعضاء الحيوية', size: 'بحجم حبة الفراولة' },
    { week: 12, length: 5.4, weight: 14, milestone: 'اكتمال الأعضاء الأساسية', size: 'بحجم حبة الليمون' },
    { week: 14, length: 8.7, weight: 43, milestone: 'نمو الشعر والحواجب', size: 'بحجم حبة الخوخ' },
    { week: 16, length: 11.6, weight: 100, milestone: 'تحديد الجنس وبداية الحركة', size: 'بحجم حبة الأفوكادو' },
    { week: 18, length: 14.2, weight: 190, milestone: 'تطور السمع', size: 'بحجم حبة الفلفل الحلو' },
    { week: 20, length: 16.4, weight: 300, milestone: 'منتصف الحمل - حركة واضحة', size: 'بحجم حبة الموز' },
    { week: 22, length: 19.0, weight: 430, milestone: 'تطور الحواس', size: 'بحجم حبة البابايا' },
    { week: 24, length: 21.3, weight: 600, milestone: 'بداية القدرة على البقاء', size: 'بحجم كوز الذرة' },
    { week: 26, length: 23.4, weight: 760, milestone: 'فتح العينين', size: 'بحجم الخس' },
    { week: 28, length: 25.4, weight: 1000, milestone: 'تطور الدماغ السريع', size: 'بحجم الباذنجان' },
    { week: 30, length: 27.4, weight: 1300, milestone: 'نمو العظام', size: 'بحجم الكرنب' },
    { week: 32, length: 29.3, weight: 1700, milestone: 'تطور الجهاز المناعي', size: 'بحجم جوز الهند' },
    { week: 34, length: 31.2, weight: 2100, milestone: 'اكتمال الرئتين تقريباً', size: 'بحجم الشمام الصغير' },
    { week: 36, length: 32.9, weight: 2600, milestone: 'اكتمال معظم الأعضاء', size: 'بحجم الخس الروماني' },
    { week: 38, length: 34.6, weight: 3100, milestone: 'الاستعداد للولادة', size: 'بحجم الكراث' },
    { week: 40, length: 36.1, weight: 3400, milestone: 'مكتمل النمو وجاهز للولادة', size: 'بحجم البطيخ الصغير' }
  ];

  // بيانات توزيع الثلثات
  const trimesterData = [
    { name: 'الثلث الأول', value: 12, color: '#ec4899', weeks: '1-12' },
    { name: 'الثلث الثاني', value: 14, color: '#8b5cf6', weeks: '13-26' },
    { name: 'الثلث الثالث', value: 14, color: '#06b6d4', weeks: '27-40' }
  ];

  // بيانات التطور الأسبوعي
  const weeklyProgressData = Array.from({ length: 40 }, (_, i) => {
    const week = i + 1;
    const progress = (week / 40) * 100;
    return {
      week,
      progress,
      current: week === results.currentWeeks
    };
  });

  // بيانات الفحوصات المهمة مع تفاصيل أكثر
  const importantCheckups = [
    { week: 6, test: 'أول زيارة طبية', importance: 'عالية', description: 'تأكيد الحمل وفحص عام' },
    { week: 8, test: 'فحص الموجات فوق الصوتية الأول', importance: 'عالية', description: 'تحديد عمر الحمل ونبضات القلب' },
    { week: 11, test: 'فحص الشفافية القفوية', importance: 'عالية', description: 'فحص التشوهات الخلقية' },
    { week: 16, test: 'فحص الدم الثلاثي', importance: 'متوسطة', description: 'فحص متلازمة داون وعيوب الأنبوب العصبي' },
    { week: 20, test: 'الموجات فوق الصوتية المفصلة', importance: 'عالية', description: 'فحص شامل لنمو الجنين وتحديد الجنس' },
    { week: 24, test: 'فحص سكري الحمل', importance: 'عالية', description: 'اختبار تحمل الجلوكوز' },
    { week: 28, test: 'فحص الأجسام المضادة', importance: 'متوسطة', description: 'فحص عامل الريسوس والأنيميا' },
    { week: 32, test: 'مراقبة النمو والوضعية', importance: 'متوسطة', description: 'تقييم نمو الجنين ووضعيته' },
    { week: 36, test: 'فحص البكتيريا العقدية', importance: 'عالية', description: 'فحص البكتيريا العقدية المجموعة ب' },
    { week: 38, test: 'تقييم الاستعداد للولادة', importance: 'عالية', description: 'فحص عنق الرحم ووضعية الجنين' }
  ];

  const currentGrowthData = fetalGrowthData.find(data => 
    data.week <= results.currentWeeks
  ) || fetalGrowthData[0];

  const nextMilestone = fetalGrowthData.find(data => 
    data.week > results.currentWeeks
  );

  return (
    <div className="space-y-6">
      {/* نظرة عامة على النمو */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card text-center relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-pink-200 to-red-200 rounded-full opacity-30 -translate-y-8 translate-x-8"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-pink-400 to-red-500 flex items-center justify-center">
              <Baby className="w-7 h-7 text-white" />
            </div>
            <h4 className="font-bold text-gray-900 mb-2">طول الجنين</h4>
            <p className="text-3xl font-bold text-pink-600 mb-1">{currentGrowthData.length} سم</p>
            <p className="text-sm text-gray-600">{currentGrowthData.size}</p>
          </div>
        </div>

        <div className="card text-center relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-purple-200 to-indigo-200 rounded-full opacity-30 -translate-y-8 translate-x-8"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-purple-400 to-indigo-500 flex items-center justify-center">
              <Weight className="w-7 h-7 text-white" />
            </div>
            <h4 className="font-bold text-gray-900 mb-2">وزن الجنين</h4>
            <p className="text-3xl font-bold text-purple-600 mb-1">{currentGrowthData.weight} جم</p>
            <p className="text-sm text-gray-600">الوزن الطبيعي</p>
          </div>
        </div>

        <div className="card text-center relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-blue-200 to-cyan-200 rounded-full opacity-30 -translate-y-8 translate-x-8"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-blue-400 to-cyan-500 flex items-center justify-center">
              <TrendingUp className="w-7 h-7 text-white" />
            </div>
            <h4 className="font-bold text-gray-900 mb-2">الأسبوع الحالي</h4>
            <p className="text-3xl font-bold text-blue-600 mb-1">{results.currentWeeks}</p>
            <p className="text-sm text-gray-600">أسبوع من الحمل</p>
          </div>
        </div>

        <div className="card text-center relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-green-200 to-emerald-200 rounded-full opacity-30 -translate-y-8 translate-x-8"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-green-400 to-emerald-500 flex items-center justify-center">
              <Calendar className="w-7 h-7 text-white" />
            </div>
            <h4 className="font-bold text-gray-900 mb-2">الثلث الحالي</h4>
            <p className="text-3xl font-bold text-green-600 mb-1">{results.trimester}</p>
            <p className="text-sm text-gray-600">من أثلاث الحمل</p>
          </div>
        </div>
      </div>

      {/* معلومات التطور الحالي */}
      <div className="card-gradient text-white relative overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
        <div className="relative z-10 text-center">
          <h3 className="text-2xl font-bold mb-4">التطور الحالي للجنين</h3>
          <p className="text-xl mb-4">{currentGrowthData.milestone}</p>
          <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 inline-block">
            <p className="text-lg font-semibold">حجم الجنين: {currentGrowthData.size}</p>
          </div>
        </div>
      </div>

      {/* رسم بياني لنمو الجنين */}
      <div className="card relative overflow-hidden">
        <div className="absolute top-0 left-0 w-20 h-20 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full opacity-50 -translate-y-10 -translate-x-10"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold flex items-center">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-pink-400 to-purple-500 flex items-center justify-center ml-3">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              منحنى نمو الجنين
            </h3>
            <div className="flex space-x-2 space-x-reverse">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-pink-500 rounded-full ml-2"></div>
                <span className="text-sm text-gray-600">الطول (سم)</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-purple-500 rounded-full ml-2"></div>
                <span className="text-sm text-gray-600">الوزن (جم)</span>
              </div>
            </div>
          </div>
          <div className="h-96 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={fetalGrowthData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis
                  dataKey="week"
                  stroke="#6b7280"
                  fontSize={12}
                  label={{ value: 'أسبوع الحمل', position: 'insideBottom', offset: -10, style: { textAnchor: 'middle', fill: '#6b7280' } }}
                />
                <YAxis
                  yAxisId="length"
                  orientation="right"
                  stroke="#ec4899"
                  fontSize={12}
                  label={{ value: 'الطول (سم)', angle: 90, position: 'insideRight', style: { textAnchor: 'middle', fill: '#ec4899' } }}
                />
                <YAxis
                  yAxisId="weight"
                  orientation="left"
                  stroke="#8b5cf6"
                  fontSize={12}
                  label={{ value: 'الوزن (جم)', angle: 90, position: 'insideLeft', style: { textAnchor: 'middle', fill: '#8b5cf6' } }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                    fontSize: '14px'
                  }}
                  labelFormatter={(value) => `الأسبوع ${value}`}
                  formatter={(value, name, props) => [
                    `${value} ${name === 'length' ? 'سم' : 'جم'}`,
                    name === 'length' ? 'الطول' : 'الوزن'
                  ]}
                />
                <Line
                  yAxisId="length"
                  type="monotone"
                  dataKey="length"
                  stroke="#ec4899"
                  strokeWidth={4}
                  dot={{ fill: '#ec4899', strokeWidth: 2, r: 6 }}
                  activeDot={{ r: 8, fill: '#ec4899', stroke: '#fff', strokeWidth: 2 }}
                  name="length"
                />
                <Line
                  yAxisId="weight"
                  type="monotone"
                  dataKey="weight"
                  stroke="#8b5cf6"
                  strokeWidth={4}
                  dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 6 }}
                  activeDot={{ r: 8, fill: '#8b5cf6', stroke: '#fff', strokeWidth: 2 }}
                  name="weight"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* نقطة الحمل الحالي */}
          <div className="mt-4 p-4 bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl border border-pink-200">
            <div className="flex items-center justify-center">
              <div className="w-4 h-4 bg-pink-500 rounded-full ml-2 animate-pulse"></div>
              <span className="font-semibold text-gray-800">
                أنت الآن في الأسبوع {results.currentWeeks} - الطول: {currentGrowthData.length} سم، الوزن: {currentGrowthData.weight} جم
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* توزيع الثلثات */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">توزيع أثلاث الحمل</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={trimesterData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {trimesterData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value, name, props) => [
                    `${value} أسبوع`,
                    props.payload.weeks
                  ]}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center space-x-4 space-x-reverse mt-4">
            {trimesterData.map((item, index) => (
              <div key={index} className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full ml-2"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm">{item.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* الفحوصات المهمة */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">الفحوصات المهمة</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={importantCheckups}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="week"
                  label={{ value: 'الأسبوع', position: 'insideBottom', offset: -5 }}
                />
                <YAxis hide />
                <Tooltip 
                  labelFormatter={(value) => `الأسبوع ${value}`}
                  formatter={(value, name, props) => [
                    props.payload.test,
                    `أهمية ${props.payload.importance}`
                  ]}
                />
                <Bar 
                  dataKey="week" 
                  fill={(entry) => {
                    const importance = importantCheckups.find(item => item.week === entry)?.importance;
                    return importance === 'عالية' ? '#ef4444' : 
                           importance === 'متوسطة' ? '#f59e0b' : '#10b981';
                  }}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* المعلم التالي */}
      {nextMilestone && (
        <div className="card bg-gradient-to-r from-pink-50 to-purple-50">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">المعلم التالي</h3>
              <p className="text-gray-700">الأسبوع {nextMilestone.week}: {nextMilestone.milestone}</p>
              <p className="text-sm text-gray-600 mt-1">
                متبقي {nextMilestone.week - results.currentWeeks} أسبوع
              </p>
            </div>
            <Calendar className="w-12 h-12 text-pink-500" />
          </div>
        </div>
      )}
    </div>
  );
}
