'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell, Bar<PERSON>hart, Bar } from 'recharts';
import { TrendingUp, Baby, Calendar, Weight } from 'lucide-react';
import { PregnancyResults } from '@/utils/pregnancyCalculations';

interface PregnancyChartsProps {
  results: PregnancyResults;
}

export default function PregnancyCharts({ results }: PregnancyChartsProps) {
  // بيانات نمو الجنين المحسنة بالذكاء الاصطناعي (معايير WHO وACOG)
  const fetalGrowthData = [
    { week: 4, length: 0.4, weight: 0.02, milestone: 'تكوين الأنبوب العصبي وبداية القلب', size: 'بذرة الخشخاش', percentile: { p10: 0.01, p50: 0.02, p90: 0.03 }, development: 'تكوين الطبقا<PERSON> الجنينية الثلاث' },
    { week: 5, length: 0.5, weight: 0.03, milestone: 'تطور الحبل الشوكي', size: 'بذرة السمسم', percentile: { p10: 0.02, p50: 0.03, p90: 0.04 }, development: 'بداية تكوين الدماغ' },
    { week: 6, length: 0.6, weight: 0.04, milestone: 'أول نبضات القلب (110-160 نبضة/دقيقة)', size: 'حبة العدس', percentile: { p10: 0.03, p50: 0.04, p90: 0.05 }, development: 'تكوين الأطراف البدائية' },
    { week: 7, length: 1.0, weight: 0.8, milestone: 'تطور الوجه والعينين', size: 'حبة التوت الأزرق', percentile: { p10: 0.6, p50: 0.8, p90: 1.0 }, development: 'تكوين الكلى والكبد' },
    { week: 8, length: 1.6, weight: 1.0, milestone: 'تكوين الأصابع والأطراف', size: 'حبة التوت', percentile: { p10: 0.8, p50: 1.0, p90: 1.2 }, development: 'بداية تكوين الأعضاء التناسلية' },
    { week: 9, length: 2.3, weight: 2.0, milestone: 'تطور العضلات والأعصاب', size: 'حبة العنب', percentile: { p10: 1.5, p50: 2.0, p90: 2.5 }, development: 'تكوين الجفون' },
    { week: 10, length: 3.1, weight: 4.0, milestone: 'تكوين الأظافر والشعر', size: 'حبة الفراولة', percentile: { p10: 3.0, p50: 4.0, p90: 5.0 }, development: 'تطور الجهاز الهضمي' },
    { week: 11, length: 4.1, weight: 7.0, milestone: 'تطور الأسنان والعظام', size: 'حبة التين', percentile: { p10: 5.5, p50: 7.0, p90: 8.5 }, development: 'بداية إنتاج البول' },
    { week: 12, length: 5.4, weight: 14.0, milestone: 'اكتمال الأعضاء الأساسية', size: 'حبة الليمون', percentile: { p10: 11.0, p50: 14.0, p90: 17.0 }, development: 'تطور ردود الأفعال' },
    { week: 13, length: 7.4, weight: 23.0, milestone: 'تطور الحبال الصوتية', size: 'حبة الخوخ الصغيرة', percentile: { p10: 18.0, p50: 23.0, p90: 28.0 }, development: 'بداية تكوين بصمات الأصابع' },
    { week: 14, length: 8.7, weight: 43.0, milestone: 'نمو الشعر والحواجب', size: 'حبة الخوخ', percentile: { p10: 35.0, p50: 43.0, p90: 51.0 }, development: 'تطور الغدد اللعابية' },
    { week: 15, length: 10.1, weight: 70.0, milestone: 'تطور حاسة التذوق', size: 'تفاحة صغيرة', percentile: { p10: 55.0, p50: 70.0, p90: 85.0 }, development: 'تكوين الدهون البنية' },
    { week: 16, length: 11.6, weight: 100.0, milestone: 'إمكانية تحديد الجنس', size: 'حبة الأفوكادو', percentile: { p10: 80.0, p50: 100.0, p90: 120.0 }, development: 'تطور الجهاز العصبي المركزي' },
    { week: 17, length: 13.0, weight: 140.0, milestone: 'تطور الدهون والعضلات', size: 'حبة الكمثرى', percentile: { p10: 110.0, p50: 140.0, p90: 170.0 }, development: 'تكوين الميلانين' },
    { week: 18, length: 14.2, weight: 190.0, milestone: 'تطور السمع وردود الأفعال', size: 'فلفل حلو', percentile: { p10: 150.0, p50: 190.0, p90: 230.0 }, development: 'تطور الجهاز المناعي' },
    { week: 19, length: 15.3, weight: 240.0, milestone: 'تطور الحواس الخمس', size: 'طماطم كبيرة', percentile: { p10: 200.0, p50: 240.0, p90: 280.0 }, development: 'تكوين الطبقة الواقية للجلد' },
    { week: 20, length: 16.4, weight: 300.0, milestone: 'منتصف الحمل - حركة واضحة', size: 'موزة', percentile: { p10: 250.0, p50: 300.0, p90: 350.0 }, development: 'تطور أنماط النوم' },
    { week: 21, length: 26.7, weight: 360.0, milestone: 'تطور الجهاز الهضمي', size: 'جزرة كبيرة', percentile: { p10: 300.0, p50: 360.0, p90: 420.0 }, development: 'بداية هضم السائل الأمنيوسي' },
    { week: 22, length: 27.8, weight: 430.0, milestone: 'تطور الحواس والذاكرة', size: 'بابايا صغيرة', percentile: { p10: 350.0, p50: 430.0, p90: 510.0 }, development: 'تكوين خلايا الدماغ' },
    { week: 23, length: 28.9, weight: 501.0, milestone: 'تطور الرئتين', size: 'مانجو', percentile: { p10: 420.0, p50: 501.0, p90: 582.0 }, development: 'إنتاج السائل السطحي' },
    { week: 24, length: 30.0, weight: 600.0, milestone: 'بداية القدرة على البقاء', size: 'كوز ذرة', percentile: { p10: 500.0, p50: 600.0, p90: 700.0 }, development: 'تطور الأوعية الدموية في الرئتين' },
    { week: 25, length: 34.6, weight: 660.0, milestone: 'تطور الشعر والأظافر', size: 'قرنبيط', percentile: { p10: 550.0, p50: 660.0, p90: 770.0 }, development: 'تطور ردود الأفعال للضوء' },
    { week: 26, length: 35.6, weight: 760.0, milestone: 'فتح العينين', size: 'خس', percentile: { p10: 630.0, p50: 760.0, p90: 890.0 }, development: 'تطور الشبكية' },
    { week: 27, length: 36.6, weight: 875.0, milestone: 'تطور الدماغ السريع', size: 'قرنبيط كبير', percentile: { p10: 720.0, p50: 875.0, p90: 1030.0 }, development: 'تكوين الأخاديد في الدماغ' },
    { week: 28, length: 37.6, weight: 1005.0, milestone: 'تطور الجهاز العصبي', size: 'باذنجان', percentile: { p10: 820.0, p50: 1005.0, p90: 1190.0 }, development: 'تطور دورات النوم والاستيقاظ' },
    { week: 29, length: 38.6, weight: 1153.0, milestone: 'تطور العضلات والعظام', size: 'اسكواش', percentile: { p10: 940.0, p50: 1153.0, p90: 1366.0 }, development: 'تطور نخاع العظام' },
    { week: 30, length: 39.9, weight: 1319.0, milestone: 'نمو الدماغ والرئتين', size: 'كرنب', percentile: { p10: 1080.0, p50: 1319.0, p90: 1558.0 }, development: 'تطور التحكم في درجة الحرارة' },
    { week: 31, length: 41.1, weight: 1502.0, milestone: 'تطور الجهاز المناعي', size: 'جوز هند', percentile: { p10: 1235.0, p50: 1502.0, p90: 1769.0 }, development: 'إنتاج خلايا الدم الحمراء' },
    { week: 32, length: 42.4, weight: 1702.0, milestone: 'تطور الأظافر والشعر', size: 'كالي', percentile: { p10: 1405.0, p50: 1702.0, p90: 1999.0 }, development: 'تطور طبقات الجلد' },
    { week: 33, length: 43.7, weight: 1918.0, milestone: 'تطور الجهاز التنفسي', size: 'أناناس', percentile: { p10: 1590.0, p50: 1918.0, p90: 2246.0 }, development: 'نضج الرئتين' },
    { week: 34, length: 45.0, weight: 2146.0, milestone: 'اكتمال الرئتين تقريباً', size: 'شمام صغير', percentile: { p10: 1785.0, p50: 2146.0, p90: 2507.0 }, development: 'تطور الجهاز الهضمي' },
    { week: 35, length: 46.2, weight: 2383.0, milestone: 'تطور الكلى والكبد', size: 'شمام عسلي', percentile: { p10: 1995.0, p50: 2383.0, p90: 2771.0 }, development: 'تطور وظائف الكبد' },
    { week: 36, length: 47.4, weight: 2622.0, milestone: 'اكتمال معظم الأعضاء', size: 'خس روماني', percentile: { p10: 2212.0, p50: 2622.0, p90: 3032.0 }, development: 'تطور الجهاز الهضمي الكامل' },
    { week: 37, length: 48.6, weight: 2859.0, milestone: 'الحمل مكتمل المدة', size: 'سلق سويسري', percentile: { p10: 2430.0, p50: 2859.0, p90: 3288.0 }, development: 'تطور المناعة الطبيعية' },
    { week: 38, length: 49.8, weight: 3083.0, milestone: 'الاستعداد للولادة', size: 'كراث', percentile: { p10: 2640.0, p50: 3083.0, p90: 3526.0 }, development: 'تطور ردود الأفعال للولادة' },
    { week: 39, length: 50.7, weight: 3288.0, milestone: 'نضج كامل للأعضاء', size: 'بطيخ صغير', percentile: { p10: 2835.0, p50: 3288.0, p90: 3741.0 }, development: 'تطور الجهاز العصبي الكامل' },
    { week: 40, length: 51.2, weight: 3462.0, milestone: 'مكتمل النمو وجاهز للولادة', size: 'بطيخ', percentile: { p10: 3008.0, p50: 3462.0, p90: 3916.0 }, development: 'جاهز للحياة خارج الرحم' },
    { week: 41, length: 51.7, weight: 3597.0, milestone: 'نمو إضافي', size: 'يقطين صغير', percentile: { p10: 3150.0, p50: 3597.0, p90: 4044.0 }, development: 'نضج إضافي للرئتين' },
    { week: 42, length: 52.0, weight: 3685.0, milestone: 'تأخر الولادة', size: 'يقطين', percentile: { p10: 3250.0, p50: 3685.0, p90: 4120.0 }, development: 'مراقبة طبية مكثفة مطلوبة' }
  ];

  // بيانات توزيع الثلثات
  const trimesterData = [
    { name: 'الثلث الأول', value: 12, color: '#ec4899', weeks: '1-12' },
    { name: 'الثلث الثاني', value: 14, color: '#8b5cf6', weeks: '13-26' },
    { name: 'الثلث الثالث', value: 14, color: '#06b6d4', weeks: '27-40' }
  ];

  // بيانات التطور الأسبوعي
  const weeklyProgressData = Array.from({ length: 40 }, (_, i) => {
    const week = i + 1;
    const progress = (week / 40) * 100;
    return {
      week,
      progress,
      current: week === results.currentWeeks
    };
  });

  // بيانات الفحوصات المهمة مع تفاصيل أكثر
  const importantCheckups = [
    { week: 6, test: 'أول زيارة طبية', importance: 'عالية', description: 'تأكيد الحمل وفحص عام' },
    { week: 8, test: 'فحص الموجات فوق الصوتية الأول', importance: 'عالية', description: 'تحديد عمر الحمل ونبضات القلب' },
    { week: 11, test: 'فحص الشفافية القفوية', importance: 'عالية', description: 'فحص التشوهات الخلقية' },
    { week: 16, test: 'فحص الدم الثلاثي', importance: 'متوسطة', description: 'فحص متلازمة داون وعيوب الأنبوب العصبي' },
    { week: 20, test: 'الموجات فوق الصوتية المفصلة', importance: 'عالية', description: 'فحص شامل لنمو الجنين وتحديد الجنس' },
    { week: 24, test: 'فحص سكري الحمل', importance: 'عالية', description: 'اختبار تحمل الجلوكوز' },
    { week: 28, test: 'فحص الأجسام المضادة', importance: 'متوسطة', description: 'فحص عامل الريسوس والأنيميا' },
    { week: 32, test: 'مراقبة النمو والوضعية', importance: 'متوسطة', description: 'تقييم نمو الجنين ووضعيته' },
    { week: 36, test: 'فحص البكتيريا العقدية', importance: 'عالية', description: 'فحص البكتيريا العقدية المجموعة ب' },
    { week: 38, test: 'تقييم الاستعداد للولادة', importance: 'عالية', description: 'فحص عنق الرحم ووضعية الجنين' }
  ];

  const currentGrowthData = fetalGrowthData.find(data => 
    data.week <= results.currentWeeks
  ) || fetalGrowthData[0];

  const nextMilestone = fetalGrowthData.find(data => 
    data.week > results.currentWeeks
  );

  return (
    <div className="space-y-6">
      {/* نظرة عامة على النمو */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card text-center relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-pink-200 to-red-200 rounded-full opacity-30 -translate-y-8 translate-x-8"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-pink-400 to-red-500 flex items-center justify-center">
              <Baby className="w-7 h-7 text-white" />
            </div>
            <h4 className="font-bold text-gray-900 mb-2">طول الجنين</h4>
            <p className="text-3xl font-bold text-pink-600 mb-1">{currentGrowthData.length} سم</p>
            <p className="text-sm text-gray-600">{currentGrowthData.size}</p>
          </div>
        </div>

        <div className="card text-center relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-purple-200 to-indigo-200 rounded-full opacity-30 -translate-y-8 translate-x-8"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-purple-400 to-indigo-500 flex items-center justify-center">
              <Weight className="w-7 h-7 text-white" />
            </div>
            <h4 className="font-bold text-gray-900 mb-2">وزن الجنين</h4>
            <p className="text-3xl font-bold text-purple-600 mb-1">{currentGrowthData.weight} جم</p>
            <p className="text-sm text-gray-600">الوزن الطبيعي</p>
          </div>
        </div>

        <div className="card text-center relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-blue-200 to-cyan-200 rounded-full opacity-30 -translate-y-8 translate-x-8"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-blue-400 to-cyan-500 flex items-center justify-center">
              <TrendingUp className="w-7 h-7 text-white" />
            </div>
            <h4 className="font-bold text-gray-900 mb-2">الأسبوع الحالي</h4>
            <p className="text-3xl font-bold text-blue-600 mb-1">{results.currentWeeks}</p>
            <p className="text-sm text-gray-600">أسبوع من الحمل</p>
          </div>
        </div>

        <div className="card text-center relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-green-200 to-emerald-200 rounded-full opacity-30 -translate-y-8 translate-x-8"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-green-400 to-emerald-500 flex items-center justify-center">
              <Calendar className="w-7 h-7 text-white" />
            </div>
            <h4 className="font-bold text-gray-900 mb-2">الثلث الحالي</h4>
            <p className="text-3xl font-bold text-green-600 mb-1">{results.trimester}</p>
            <p className="text-sm text-gray-600">من أثلاث الحمل</p>
          </div>
        </div>
      </div>

      {/* معلومات التطور الحالي */}
      <div className="card-gradient text-white relative overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
        <div className="relative z-10 text-center">
          <h3 className="text-2xl font-bold mb-4">التطور الحالي للجنين</h3>
          <p className="text-xl mb-4">{currentGrowthData.milestone}</p>
          <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 inline-block">
            <p className="text-lg font-semibold">حجم الجنين: {currentGrowthData.size}</p>
          </div>
        </div>
      </div>

      {/* رسم بياني لنمو الجنين */}
      <div className="card relative overflow-hidden">
        <div className="absolute top-0 left-0 w-20 h-20 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full opacity-50 -translate-y-10 -translate-x-10"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold flex items-center">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-pink-400 to-purple-500 flex items-center justify-center ml-3">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              منحنى نمو الجنين
            </h3>
            <div className="flex space-x-2 space-x-reverse">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-pink-500 rounded-full ml-2"></div>
                <span className="text-sm text-gray-600">الطول (سم)</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-purple-500 rounded-full ml-2"></div>
                <span className="text-sm text-gray-600">الوزن (جم)</span>
              </div>
            </div>
          </div>
          <div className="h-96 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={fetalGrowthData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis
                  dataKey="week"
                  stroke="#6b7280"
                  fontSize={12}
                  label={{ value: 'أسبوع الحمل', position: 'insideBottom', offset: -10, style: { textAnchor: 'middle', fill: '#6b7280' } }}
                />
                <YAxis
                  yAxisId="length"
                  orientation="right"
                  stroke="#ec4899"
                  fontSize={12}
                  label={{ value: 'الطول (سم)', angle: 90, position: 'insideRight', style: { textAnchor: 'middle', fill: '#ec4899' } }}
                />
                <YAxis
                  yAxisId="weight"
                  orientation="left"
                  stroke="#8b5cf6"
                  fontSize={12}
                  label={{ value: 'الوزن (جم)', angle: 90, position: 'insideLeft', style: { textAnchor: 'middle', fill: '#8b5cf6' } }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                    fontSize: '14px'
                  }}
                  labelFormatter={(value) => `الأسبوع ${value}`}
                  formatter={(value, name, props) => [
                    `${value} ${name === 'length' ? 'سم' : 'جم'}`,
                    name === 'length' ? 'الطول' : 'الوزن'
                  ]}
                />
                <Line
                  yAxisId="length"
                  type="monotone"
                  dataKey="length"
                  stroke="#ec4899"
                  strokeWidth={4}
                  dot={{ fill: '#ec4899', strokeWidth: 2, r: 6 }}
                  activeDot={{ r: 8, fill: '#ec4899', stroke: '#fff', strokeWidth: 2 }}
                  name="length"
                />
                <Line
                  yAxisId="weight"
                  type="monotone"
                  dataKey="weight"
                  stroke="#8b5cf6"
                  strokeWidth={4}
                  dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 6 }}
                  activeDot={{ r: 8, fill: '#8b5cf6', stroke: '#fff', strokeWidth: 2 }}
                  name="weight"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* نقطة الحمل الحالي */}
          <div className="mt-4 p-4 bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl border border-pink-200">
            <div className="flex items-center justify-center">
              <div className="w-4 h-4 bg-pink-500 rounded-full ml-2 animate-pulse"></div>
              <span className="font-semibold text-gray-800">
                أنت الآن في الأسبوع {results.currentWeeks} - الطول: {currentGrowthData.length} سم، الوزن: {currentGrowthData.weight} جم
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* توزيع الثلثات */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">توزيع أثلاث الحمل</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={trimesterData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {trimesterData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value, name, props) => [
                    `${value} أسبوع`,
                    props.payload.weeks
                  ]}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center space-x-4 space-x-reverse mt-4">
            {trimesterData.map((item, index) => (
              <div key={index} className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full ml-2"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm">{item.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* الفحوصات المهمة */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">الفحوصات المهمة</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={importantCheckups}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="week"
                  label={{ value: 'الأسبوع', position: 'insideBottom', offset: -5 }}
                />
                <YAxis hide />
                <Tooltip 
                  labelFormatter={(value) => `الأسبوع ${value}`}
                  formatter={(value, name, props) => [
                    props.payload.test,
                    `أهمية ${props.payload.importance}`
                  ]}
                />
                <Bar 
                  dataKey="week" 
                  fill={(entry) => {
                    const importance = importantCheckups.find(item => item.week === entry)?.importance;
                    return importance === 'عالية' ? '#ef4444' : 
                           importance === 'متوسطة' ? '#f59e0b' : '#10b981';
                  }}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* المعلم التالي */}
      {nextMilestone && (
        <div className="card bg-gradient-to-r from-pink-50 to-purple-50">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">المعلم التالي</h3>
              <p className="text-gray-700">الأسبوع {nextMilestone.week}: {nextMilestone.milestone}</p>
              <p className="text-sm text-gray-600 mt-1">
                متبقي {nextMilestone.week - results.currentWeeks} أسبوع
              </p>
            </div>
            <Calendar className="w-12 h-12 text-pink-500" />
          </div>
        </div>
      )}
    </div>
  );
}
