'use client';

import { Calendar, Baby, Heart, Brain, Eye, Ear } from 'lucide-react';
import { motion } from 'framer-motion';

interface WeeklyDetailsProps {
  week: number;
  trimester: 1 | 2 | 3;
}

export default function WeeklyDetails({ week, trimester }: WeeklyDetailsProps) {
  const getWeeklyInfo = (week: number) => {
    const weeklyData: { [key: number]: any } = {
      4: {
        title: 'بداية الرحلة',
        development: 'تكوين الأنبوب العصبي وبداية تطور القلب',
        size: 'بحجم بذرة الخشخاش',
        symptoms: ['غياب الدورة الشهرية', 'تعب خفيف'],
        tips: ['ابدئي بتناول حمض الفوليك', 'تجنبي الكحول والتدخين'],
        icon: Heart
      },
      8: {
        title: 'تطور الأعضاء',
        development: 'تكوين الأطراف والوجه، بداية نبضات القلب',
        size: 'بحجم حبة التوت',
        symptoms: ['غثيان صباحي', 'حساسية للروائح', 'ألم الثدي'],
        tips: ['تناولي وجبات صغيرة متكررة', 'اشربي الزنجبيل للغثيان'],
        icon: Baby
      },
      12: {
        title: 'نهاية الثلث الأول',
        development: 'اكتمال الأعضاء الأساسية، بداية تكوين الأظافر',
        size: 'بحجم حبة الليمون',
        symptoms: ['تحسن الغثيان', 'زيادة الطاقة'],
        tips: ['موعد أول فحص بالموجات فوق الصوتية', 'ابدئي بإخبار الآخرين'],
        icon: Calendar
      },
      16: {
        title: 'منتصف الحمل يقترب',
        development: 'تطور الجهاز العصبي، إمكانية تحديد الجنس',
        size: 'بحجم حبة الأفوكادو',
        symptoms: ['شعور بالراحة', 'بداية ظهور البطن'],
        tips: ['فحص الدم الثلاثي', 'ابدئي بالتمارين الآمنة'],
        icon: Brain
      },
      20: {
        title: 'منتصف الحمل',
        development: 'تطور السمع، حركة واضحة للجنين',
        size: 'بحجم حبة الموز',
        symptoms: ['حركة الجنين', 'زيادة الشهية'],
        tips: ['فحص الموجات فوق الصوتية المفصل', 'ابدئي بالحديث مع الجنين'],
        icon: Ear
      },
      24: {
        title: 'بداية القدرة على البقاء',
        development: 'تطور الرئتين، فتح العينين',
        size: 'بحجم كوز الذرة',
        symptoms: ['ضيق تنفس خفيف', 'ألم الظهر'],
        tips: ['فحص سكري الحمل', 'راقبي حركة الجنين'],
        icon: Eye
      },
      28: {
        title: 'الثلث الأخير',
        development: 'نمو الدماغ السريع، تطور الجهاز المناعي',
        size: 'بحجم الباذنجان',
        symptoms: ['تورم القدمين', 'صعوبة النوم'],
        tips: ['ابدئي بدورات الولادة', 'راقبي ضغط الدم'],
        icon: Brain
      },
      32: {
        title: 'النمو السريع',
        development: 'تطور العظام، اكتساب الوزن',
        size: 'بحجم جوز الهند',
        symptoms: ['حرقة المعدة', 'كثرة التبول'],
        tips: ['تحضير حقيبة المستشفى', 'تمارين التنفس'],
        icon: Baby
      },
      36: {
        title: 'الاستعداد للولادة',
        development: 'اكتمال الرئتين، نزول الجنين للحوض',
        size: 'بحجم الخس الروماني',
        symptoms: ['ضغط على الحوض', 'تحسن التنفس'],
        tips: ['فحص البكتيريا العقدية', 'استعدي للولادة'],
        icon: Heart
      },
      40: {
        title: 'موعد الولادة',
        development: 'مكتمل النمو وجاهز للولادة',
        size: 'بحجم البطيخ الصغير',
        symptoms: ['انقباضات', 'نزول الماء'],
        tips: ['كوني مستعدة في أي وقت', 'راقبي علامات الولادة'],
        icon: Baby
      }
    };

    // العثور على أقرب أسبوع متاح
    const availableWeeks = Object.keys(weeklyData).map(Number).sort((a, b) => a - b);
    const closestWeek = availableWeeks.reduce((prev, curr) => 
      Math.abs(curr - week) < Math.abs(prev - week) ? curr : prev
    );

    return weeklyData[closestWeek] || weeklyData[40];
  };

  const weekInfo = getWeeklyInfo(week);
  const IconComponent = weekInfo.icon;

  const getTrimesterColor = (trimester: number) => {
    switch (trimester) {
      case 1: return 'from-green-400 to-emerald-500';
      case 2: return 'from-blue-400 to-cyan-500';
      case 3: return 'from-purple-400 to-pink-500';
      default: return 'from-gray-400 to-gray-500';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="card relative overflow-hidden"
    >
      {/* خلفية زخرفية */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full opacity-30 -translate-y-16 translate-x-16"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-blue-100 to-cyan-100 rounded-full opacity-30 translate-y-12 -translate-x-12"></div>

      <div className="relative z-10">
        {/* رأس البطاقة */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${getTrimesterColor(trimester)} flex items-center justify-center ml-3`}>
              <IconComponent className="w-7 h-7 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900">{weekInfo.title}</h3>
              <p className="text-gray-600">الأسبوع {week} من الحمل</p>
            </div>
          </div>
          <div className="pregnancy-week-indicator">
            الثلث {trimester}
          </div>
        </div>

        {/* معلومات التطور */}
        <div className="grid md:grid-cols-2 gap-6 mb-6">
          <div className="fetal-size-comparison">
            <h4 className="font-bold text-gray-800 mb-2">حجم الجنين</h4>
            <p className="text-lg text-purple-700 font-semibold">{weekInfo.size}</p>
          </div>
          <div className="bg-gradient-to-br from-blue-50 to-cyan-50 p-4 rounded-xl border border-blue-200">
            <h4 className="font-bold text-gray-800 mb-2">التطور الحالي</h4>
            <p className="text-blue-700">{weekInfo.development}</p>
          </div>
        </div>

        {/* الأعراض المتوقعة */}
        <div className="mb-6">
          <h4 className="font-bold text-gray-800 mb-3 flex items-center">
            <Heart className="w-5 h-5 text-red-500 ml-2" />
            الأعراض المتوقعة
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {weekInfo.symptoms.map((symptom: string, index: number) => (
              <div key={index} className="flex items-center bg-red-50 p-3 rounded-lg">
                <div className="w-2 h-2 bg-red-400 rounded-full ml-2"></div>
                <span className="text-red-700 text-sm">{symptom}</span>
              </div>
            ))}
          </div>
        </div>

        {/* النصائح */}
        <div>
          <h4 className="font-bold text-gray-800 mb-3 flex items-center">
            <Calendar className="w-5 h-5 text-green-500 ml-2" />
            نصائح مهمة
          </h4>
          <div className="space-y-2">
            {weekInfo.tips.map((tip: string, index: number) => (
              <div key={index} className="success-card">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 ml-3 mt-0.5">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <p className="text-green-800 font-medium">{tip}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* شريط التقدم الأسبوعي */}
        <div className="mt-6 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-600">تقدم الأسبوع</span>
            <span className="text-sm font-bold text-gray-800">{week}/40</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="h-3 rounded-full bg-gradient-to-r from-pink-400 to-purple-500 transition-all duration-500"
              style={{ width: `${(week / 40) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
