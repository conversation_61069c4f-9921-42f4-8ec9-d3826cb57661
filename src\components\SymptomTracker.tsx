'use client';

import { useState, useEffect } from 'react';
import { Activity, Plus, TrendingUp, AlertCircle, CheckCircle } from 'lucide-react';
import { PregnancyAI } from '@/utils/aiAssistant';
import { motion } from 'framer-motion';

interface Symptom {
  id: string;
  name: string;
  severity: 1 | 2 | 3 | 4 | 5;
  date: Date;
  notes?: string;
}

interface SymptomTrackerProps {
  pregnancyWeek: number;
}

const commonSymptoms = [
  'غثيان', 'تعب وإرهاق', 'صداع', 'دوخة', 'حرقة معدة', 'إمساك',
  'ألم الظهر', 'تورم القدمين', 'صعوبة النوم', 'تقلبات مزاجية',
  'ألم الثدي', 'كثرة التبول', 'تشنجات خفيفة', 'نزيف خفيف',
  'تغيرات في الشهية', 'حساسية للروائح', 'ضيق التنفس', 'دوالي الساقين'
];

const severityLabels = {
  1: 'خفيف جداً',
  2: 'خفيف', 
  3: 'متوسط',
  4: 'شديد',
  5: 'شديد جداً'
};

const severityColors = {
  1: 'bg-green-100 text-green-800',
  2: 'bg-yellow-100 text-yellow-800',
  3: 'bg-orange-100 text-orange-800', 
  4: 'bg-red-100 text-red-800',
  5: 'bg-red-200 text-red-900'
};

export default function SymptomTracker({ pregnancyWeek }: SymptomTrackerProps) {
  const [symptoms, setSymptoms] = useState<Symptom[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedSymptom, setSelectedSymptom] = useState('');
  const [severity, setSeverity] = useState<1 | 2 | 3 | 4 | 5>(1);
  const [notes, setNotes] = useState('');
  const [analysis, setAnalysis] = useState<any>(null);
  
  const ai = new PregnancyAI();

  useEffect(() => {
    // تحليل الأعراض عند تغييرها
    if (symptoms.length > 0) {
      const recentSymptoms = symptoms
        .filter(s => {
          const daysDiff = (new Date().getTime() - s.date.getTime()) / (1000 * 60 * 60 * 24);
          return daysDiff <= 7; // آخر أسبوع
        })
        .map(s => s.name);
      
      const analysisResult = ai.analyzeSymptoms(recentSymptoms, pregnancyWeek);
      setAnalysis(analysisResult);
    }
  }, [symptoms, pregnancyWeek]);

  const addSymptom = () => {
    if (!selectedSymptom) return;

    const newSymptom: Symptom = {
      id: Date.now().toString(),
      name: selectedSymptom,
      severity,
      date: new Date(),
      notes: notes.trim() || undefined
    };

    setSymptoms(prev => [newSymptom, ...prev]);
    
    // إعادة تعيين النموذج
    setSelectedSymptom('');
    setSeverity(1);
    setNotes('');
    setShowAddForm(false);
  };

  const getSymptomTrend = (symptomName: string) => {
    const symptomHistory = symptoms
      .filter(s => s.name === symptomName)
      .sort((a, b) => a.date.getTime() - b.date.getTime());
    
    if (symptomHistory.length < 2) return 'stable';
    
    const recent = symptomHistory.slice(-2);
    if (recent[1].severity > recent[0].severity) return 'increasing';
    if (recent[1].severity < recent[0].severity) return 'decreasing';
    return 'stable';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing':
        return <TrendingUp className="w-4 h-4 text-red-500" />;
      case 'decreasing':
        return <TrendingUp className="w-4 h-4 text-green-500 transform rotate-180" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const groupedSymptoms = symptoms.reduce((acc, symptom) => {
    if (!acc[symptom.name]) {
      acc[symptom.name] = [];
    }
    acc[symptom.name].push(symptom);
    return acc;
  }, {} as Record<string, Symptom[]>);

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold flex items-center">
          <Activity className="w-6 h-6 ml-2 text-blue-500" />
          متتبع الأعراض الذكي
        </h3>
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          className="btn-primary flex items-center"
        >
          <Plus className="w-4 h-4 ml-1" />
          إضافة عرض
        </button>
      </div>

      {/* نموذج إضافة عرض */}
      {showAddForm && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="bg-gray-50 p-4 rounded-lg mb-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">نوع العرض</label>
              <select
                value={selectedSymptom}
                onChange={(e) => setSelectedSymptom(e.target.value)}
                className="input-field"
              >
                <option value="">اختاري العرض</option>
                {commonSymptoms.map(symptom => (
                  <option key={symptom} value={symptom}>{symptom}</option>
                ))}
                <option value="أخرى">أخرى</option>
              </select>
              {selectedSymptom === 'أخرى' && (
                <input
                  type="text"
                  placeholder="اكتبي العرض"
                  value={notes}
                  onChange={(e) => setSelectedSymptom(e.target.value)}
                  className="input-field mt-2"
                />
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">شدة العرض</label>
              <select
                value={severity}
                onChange={(e) => setSeverity(Number(e.target.value) as 1 | 2 | 3 | 4 | 5)}
                className="input-field"
              >
                {Object.entries(severityLabels).map(([value, label]) => (
                  <option key={value} value={value}>{label}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium mb-2">ملاحظات إضافية</label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="أي تفاصيل إضافية..."
              className="input-field h-20 resize-none"
            />
          </div>

          <div className="flex justify-end space-x-2 space-x-reverse mt-4">
            <button
              onClick={() => setShowAddForm(false)}
              className="btn-secondary"
            >
              إلغاء
            </button>
            <button
              onClick={addSymptom}
              disabled={!selectedSymptom}
              className="btn-primary disabled:opacity-50"
            >
              إضافة
            </button>
          </div>
        </motion.div>
      )}

      {/* تحليل الأعراض */}
      {analysis && (
        <div className={`p-4 rounded-lg mb-6 ${
          analysis.urgency === 'high' ? 'bg-red-50 border border-red-200' :
          analysis.urgency === 'medium' ? 'bg-yellow-50 border border-yellow-200' :
          'bg-green-50 border border-green-200'
        }`}>
          <div className="flex items-center mb-2">
            {analysis.urgency === 'high' ? (
              <AlertCircle className="w-5 h-5 text-red-500 ml-2" />
            ) : (
              <CheckCircle className="w-5 h-5 text-green-500 ml-2" />
            )}
            <h4 className="font-semibold">تحليل الأعراض الذكي</h4>
          </div>
          <p className="text-sm mb-3">{analysis.analysis}</p>
          <div>
            <h5 className="font-medium text-sm mb-1">التوصيات:</h5>
            <ul className="text-sm space-y-1">
              {analysis.recommendations.map((rec: string, index: number) => (
                <li key={index}>• {rec}</li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* قائمة الأعراض */}
      <div className="space-y-4">
        {Object.entries(groupedSymptoms).map(([symptomName, symptomList]) => {
          const latestSymptom = symptomList[0];
          const trend = getSymptomTrend(symptomName);
          
          return (
            <div key={symptomName} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <h4 className="font-medium">{symptomName}</h4>
                  {getTrendIcon(trend)}
                </div>
                <span className={`px-2 py-1 rounded-full text-xs ${severityColors[latestSymptom.severity]}`}>
                  {severityLabels[latestSymptom.severity]}
                </span>
              </div>
              
              <div className="text-sm text-gray-600">
                <p>آخر تسجيل: {latestSymptom.date.toLocaleDateString('ar-SA')}</p>
                {latestSymptom.notes && (
                  <p className="mt-1">ملاحظات: {latestSymptom.notes}</p>
                )}
                <p className="mt-1">عدد المرات: {symptomList.length}</p>
              </div>
            </div>
          );
        })}
      </div>

      {symptoms.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Activity className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>لم يتم تسجيل أي أعراض بعد</p>
          <p className="text-sm">ابدئي بتسجيل أعراضك للحصول على تحليل ذكي</p>
        </div>
      )}
    </div>
  );
}
