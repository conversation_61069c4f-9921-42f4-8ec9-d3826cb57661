'use client';

import { useState, useEffect } from 'react';
import { Bell, Calendar, Pill, Stethoscope, CheckC<PERSON>cle, Clock, AlertTriangle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { addDays, differenceInDays, format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface Reminder {
  id: string;
  type: 'checkup' | 'medication' | 'appointment' | 'general';
  title: string;
  description: string;
  dueDate: Date;
  priority: 'low' | 'medium' | 'high';
  completed: boolean;
  pregnancyWeek: number;
}

interface SmartRemindersProps {
  pregnancyWeek: number;
  lastMenstrualPeriod: Date;
}

export default function SmartReminders({ pregnancyWeek, lastMenstrualPeriod }: SmartRemindersProps) {
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [showCompleted, setShowCompleted] = useState(false);

  useEffect(() => {
    generateSmartReminders();
  }, [pregnancyWeek, lastMenstrualPeriod]);

  const generateSmartReminders = () => {
    const today = new Date();
    const generatedReminders: Reminder[] = [];

    // تذكيرات الفحوصات الطبية
    const medicalCheckups = [
      { week: 6, title: 'أول زيارة طبية', description: 'تأكيد الحمل وفحص عام', priority: 'high' as const },
      { week: 11, title: 'فحص الشفافية القفوية', description: 'فحص للكشف عن التشوهات الخلقية', priority: 'high' as const },
      { week: 16, title: 'فحص الدم الثلاثي', description: 'فحص للكشف عن متلازمة داون', priority: 'medium' as const },
      { week: 20, title: 'الموجات فوق الصوتية المفصلة', description: 'فحص شامل لنمو الجنين', priority: 'high' as const },
      { week: 24, title: 'فحص السكري', description: 'فحص سكري الحمل', priority: 'high' as const },
      { week: 28, title: 'فحص الأجسام المضادة', description: 'فحص عامل الريسوس', priority: 'medium' as const },
      { week: 32, title: 'مراقبة النمو', description: 'متابعة نمو الجنين ووضعيته', priority: 'medium' as const },
      { week: 36, title: 'فحص البكتيريا العقدية', description: 'فحص البكتيريا العقدية المجموعة ب', priority: 'high' as const }
    ];

    medicalCheckups.forEach((checkup, index) => {
      const dueDate = addDays(lastMenstrualPeriod, checkup.week * 7);
      const daysDiff = differenceInDays(dueDate, today);
      
      if (daysDiff >= -7 && daysDiff <= 14) { // إظهار التذكيرات قبل أسبوع وبعد أسبوعين
        generatedReminders.push({
          id: `checkup-${index}`,
          type: 'checkup',
          title: checkup.title,
          description: checkup.description,
          dueDate,
          priority: checkup.priority,
          completed: false,
          pregnancyWeek: checkup.week
        });
      }
    });

    // تذكيرات الأدوية والفيتامينات
    const medications = [
      { title: 'حمض الفوليك', description: 'تناولي 400-800 ميكروجرام يومياً لمنع عيوب الأنبوب العصبي', frequency: 'daily' },
      { title: 'فيتامينات الحمل', description: 'تناولي الفيتامينات المتعددة المخصصة للحمل', frequency: 'daily' },
      { title: 'الكالسيوم', description: 'تناولي 1000-1300 مجم يومياً لصحة العظام', frequency: 'daily' },
      { title: 'الحديد', description: 'تناولي مكملات الحديد لمنع الأنيميا (حسب توصية الطبيب)', frequency: 'daily' },
      { title: 'فيتامين د', description: 'تناولي 600-800 وحدة دولية يومياً', frequency: 'daily' },
      { title: 'أوميجا 3', description: 'تناولي مكملات أوميجا 3 لتطور دماغ الجنين', frequency: 'daily' }
    ];

    medications.forEach((med, index) => {
      generatedReminders.push({
        id: `medication-${index}`,
        type: 'medication',
        title: med.title,
        description: med.description,
        dueDate: today,
        priority: 'medium',
        completed: false,
        pregnancyWeek
      });
    });

    // تذكيرات عامة حسب الأسبوع
    const weeklyReminders = getWeeklyReminders(pregnancyWeek);
    weeklyReminders.forEach((reminder, index) => {
      generatedReminders.push({
        id: `weekly-${index}`,
        type: 'general',
        title: reminder.title,
        description: reminder.description,
        dueDate: today,
        priority: reminder.priority,
        completed: false,
        pregnancyWeek
      });
    });

    setReminders(generatedReminders);
  };

  const getWeeklyReminders = (week: number) => {
    const reminders = [];

    if (week <= 12) {
      reminders.push(
        { title: 'تجنبي الكافيين', description: 'قللي من تناول القهوة والشاي', priority: 'medium' as const },
        { title: 'تناولي الزنجبيل', description: 'يساعد في تخفيف الغثيان', priority: 'low' as const }
      );
    } else if (week <= 26) {
      reminders.push(
        { title: 'ابدئي بتمارين كيجل', description: 'تقوي عضلات الحوض', priority: 'medium' as const },
        { title: 'راقبي حركة الجنين', description: 'ستبدئين بالشعور بالحركة قريباً', priority: 'low' as const }
      );
    } else {
      reminders.push(
        { title: 'احضري دورة الولادة', description: 'تعلمي تقنيات التنفس والاسترخاء', priority: 'high' as const },
        { title: 'جهزي حقيبة المستشفى', description: 'ابدئي بتحضير الأغراض اللازمة', priority: 'medium' as const }
      );
    }

    return reminders;
  };

  const toggleReminder = (id: string) => {
    setReminders(prev => prev.map(reminder => 
      reminder.id === id 
        ? { ...reminder, completed: !reminder.completed }
        : reminder
    ));
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'checkup': return <Stethoscope className="w-5 h-5" />;
      case 'medication': return <Pill className="w-5 h-5" />;
      case 'appointment': return <Calendar className="w-5 h-5" />;
      default: return <Bell className="w-5 h-5" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-green-600 bg-green-50 border-green-200';
    }
  };

  const getDaysUntilDue = (dueDate: Date) => {
    const days = differenceInDays(dueDate, new Date());
    if (days < 0) return `متأخر ${Math.abs(days)} يوم`;
    if (days === 0) return 'اليوم';
    if (days === 1) return 'غداً';
    return `خلال ${days} أيام`;
  };

  const activeReminders = reminders.filter(r => !r.completed);
  const completedReminders = reminders.filter(r => r.completed);

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold flex items-center">
          <Bell className="w-6 h-6 ml-2 text-purple-500" />
          التذكيرات الذكية
        </h3>
        <div className="flex items-center space-x-2 space-x-reverse">
          <span className="text-sm text-gray-600">إظهار المكتملة</span>
          <button
            onClick={() => setShowCompleted(!showCompleted)}
            className={`w-10 h-6 rounded-full transition-colors ${
              showCompleted ? 'bg-purple-600' : 'bg-gray-300'
            }`}
          >
            <div className={`w-4 h-4 bg-white rounded-full transition-transform ${
              showCompleted ? 'translate-x-4' : 'translate-x-1'
            }`} />
          </button>
        </div>
      </div>

      <div className="space-y-4">
        {/* التذكيرات النشطة */}
        <AnimatePresence>
          {activeReminders.map((reminder) => (
            <motion.div
              key={reminder.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className={`border rounded-lg p-4 ${getPriorityColor(reminder.priority)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 space-x-reverse">
                  <div className="mt-1">
                    {getIcon(reminder.type)}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold">{reminder.title}</h4>
                    <p className="text-sm opacity-80 mt-1">{reminder.description}</p>
                    <div className="flex items-center space-x-2 space-x-reverse mt-2 text-xs">
                      <Clock className="w-3 h-3" />
                      <span>{getDaysUntilDue(reminder.dueDate)}</span>
                      {reminder.type === 'checkup' && (
                        <>
                          <span>•</span>
                          <span>الأسبوع {reminder.pregnancyWeek}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => toggleReminder(reminder.id)}
                  className="text-gray-400 hover:text-green-600 transition-colors"
                >
                  <CheckCircle className="w-5 h-5" />
                </button>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* التذكيرات المكتملة */}
        {showCompleted && completedReminders.length > 0 && (
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-600 mb-3">مكتملة</h4>
            {completedReminders.map((reminder) => (
              <div
                key={reminder.id}
                className="border border-gray-200 rounded-lg p-4 opacity-60"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <div>
                      <h4 className="font-medium line-through">{reminder.title}</h4>
                      <p className="text-sm text-gray-600">{reminder.description}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => toggleReminder(reminder.id)}
                    className="text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <AlertTriangle className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeReminders.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Bell className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>لا توجد تذكيرات نشطة حالياً</p>
            <p className="text-sm">ستظهر التذكيرات حسب مرحلة حملك</p>
          </div>
        )}
      </div>
    </div>
  );
}
