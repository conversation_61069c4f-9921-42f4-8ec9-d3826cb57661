'use client';

import { useState, useRef, useEffect } from 'react';
import { Bot, Send, MessageCircle, X, AlertTriangle, Lightbulb, HelpCircle } from 'lucide-react';
import { PregnancyAI, AIResponse } from '@/utils/aiAssistant';
import { motion, AnimatePresence } from 'framer-motion';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  aiResponse?: AIResponse;
}

interface AIAssistantProps {
  pregnancyWeek?: number;
  isOpen: boolean;
  onToggle: () => void;
}

export default function AIAssistant({ pregnancyWeek, isOpen, onToggle }: AIAssistantProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const ai = new PregnancyAI();

  const suggestedQuestions = [
    'ما هي أعراض الحمل الطبيعية؟',
    'ما الأطعمة المفيدة للحامل؟',
    'هل يمكنني ممارسة الرياضة؟',
    'متى أحتاج لزيارة الطبيب؟',
    'كيف أتعامل مع الغثيان؟'
  ];

  useEffect(() => {
    if (messages.length === 0) {
      const welcomeMessage: Message = {
        id: '1',
        type: 'ai',
        content: `مرحباً! أنا مساعدك الذكي للحمل 🤖\n\nيمكنني مساعدتك في:\n• الإجابة على أسئلة الحمل\n• تقديم نصائح مخصصة\n• تحليل الأعراض\n• تقديم توصيات صحية\n\nما الذي تودين معرفته اليوم؟`,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (question?: string) => {
    const messageText = question || inputValue.trim();
    if (!messageText) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: messageText,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // محاكاة تأخير الاستجابة
    setTimeout(() => {
      const aiResponse = ai.analyzeQuestion(messageText, pregnancyWeek);
      
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.answer,
        timestamp: new Date(),
        aiResponse
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) {
    return (
      <motion.button
        onClick={onToggle}
        className="fixed bottom-6 left-6 text-white p-4 rounded-2xl shadow-2xl z-50 pulse-glow"
        style={{ background: 'linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%)' }}
        whileHover={{ scale: 1.1, rotate: 5 }}
        whileTap={{ scale: 0.9 }}
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ type: "spring", stiffness: 260, damping: 20 }}
      >
        <Bot className="w-7 h-7" />
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-red-500 rounded-full animate-ping"></div>
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-red-500 rounded-full"></div>
      </motion.button>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 100, scale: 0.8 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: 100, scale: 0.8 }}
      className="fixed bottom-6 left-6 w-96 h-[600px] bg-white/90 backdrop-blur-xl rounded-2xl shadow-2xl z-50 flex flex-col border border-white/20"
      style={{ boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)' }}
    >
      {/* Header */}
      <div className="p-6 rounded-t-2xl flex items-center justify-between relative overflow-hidden"
           style={{ background: 'linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%)' }}>
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="relative z-10 flex items-center space-x-3 space-x-reverse">
          <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
            <Bot className="w-7 h-7 text-white" />
          </div>
          <div>
            <h3 className="font-bold text-lg text-white">مساعد الحمل الذكي</h3>
            <p className="text-sm text-white/80">متاح 24/7 لمساعدتك ✨</p>
          </div>
        </div>
        <button
          onClick={onToggle}
          className="relative z-10 text-white/80 hover:text-white bg-white/20 backdrop-blur-sm p-2 rounded-xl transition-all duration-300 hover:bg-white/30"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] p-3 rounded-lg ${
                message.type === 'user'
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-800'
              }`}
            >
              <p className="text-sm whitespace-pre-line">{message.content}</p>
              
              {message.aiResponse && (
                <div className="mt-3 space-y-2">
                  {/* Tips */}
                  {message.aiResponse.tips.length > 0 && (
                    <div className="bg-blue-50 p-2 rounded border-r-4 border-blue-400">
                      <div className="flex items-center mb-1">
                        <Lightbulb className="w-4 h-4 text-blue-600 ml-1" />
                        <span className="text-xs font-semibold text-blue-800">نصائح مفيدة:</span>
                      </div>
                      <ul className="text-xs text-blue-700 space-y-1">
                        {message.aiResponse.tips.map((tip, index) => (
                          <li key={index}>• {tip}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Warnings */}
                  {message.aiResponse.warnings && message.aiResponse.warnings.length > 0 && (
                    <div className="bg-yellow-50 p-2 rounded border-r-4 border-yellow-400">
                      <div className="flex items-center mb-1">
                        <AlertTriangle className="w-4 h-4 text-yellow-600 ml-1" />
                        <span className="text-xs font-semibold text-yellow-800">تنبيه:</span>
                      </div>
                      <ul className="text-xs text-yellow-700 space-y-1">
                        {message.aiResponse.warnings.map((warning, index) => (
                          <li key={index}>• {warning}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Related Questions */}
                  {message.aiResponse.relatedQuestions.length > 0 && (
                    <div className="space-y-1">
                      <span className="text-xs font-semibold text-gray-600">أسئلة ذات صلة:</span>
                      {message.aiResponse.relatedQuestions.map((question, index) => (
                        <button
                          key={index}
                          onClick={() => handleSendMessage(question)}
                          className="block text-xs text-primary-600 hover:text-primary-800 underline"
                        >
                          {question}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}
              
              <p className="text-xs opacity-70 mt-2">
                {message.timestamp.toLocaleTimeString('ar-SA', { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </p>
            </div>
          </div>
        ))}

        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 p-3 rounded-lg">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Suggested Questions */}
      {messages.length <= 1 && (
        <div className="p-3 border-t border-gray-200">
          <p className="text-xs text-gray-600 mb-2">أسئلة مقترحة:</p>
          <div className="space-y-1">
            {suggestedQuestions.slice(0, 3).map((question, index) => (
              <button
                key={index}
                onClick={() => handleSendMessage(question)}
                className="block w-full text-right text-xs text-primary-600 hover:text-primary-800 p-1 hover:bg-primary-50 rounded"
              >
                <HelpCircle className="w-3 h-3 inline ml-1" />
                {question}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2 space-x-reverse">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="اكتبي سؤالك هنا..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
            disabled={isTyping}
          />
          <button
            onClick={() => handleSendMessage()}
            disabled={!inputValue.trim() || isTyping}
            className="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </div>
    </motion.div>
  );
}
