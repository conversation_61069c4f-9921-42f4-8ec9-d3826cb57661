'use client';

import { useState, useEffect } from 'react';
import { Baby, Clock, Play, Pause, RotateCcw, TrendingUp, AlertTriangle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface MovementSession {
  id: string;
  date: Date;
  startTime: Date;
  endTime?: Date;
  movements: number;
  duration: number; // بالدقائق
  week: number;
}

interface FetalMovementTrackerProps {
  pregnancyWeek: number;
}

export default function FetalMovementTracker({ pregnancyWeek }: FetalMovementTrackerProps) {
  const [sessions, setSessions] = useState<MovementSession[]>([]);
  const [isTracking, setIsTracking] = useState(false);
  const [currentSession, setCurrentSession] = useState<MovementSession | null>(null);
  const [movementCount, setMovementCount] = useState(0);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);

  // تحديث الوقت المنقضي
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTracking && startTime) {
      interval = setInterval(() => {
        setElapsedTime(Math.floor((new Date().getTime() - startTime.getTime()) / 1000));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTracking, startTime]);

  // بدء جلسة تتبع
  const startTracking = () => {
    const now = new Date();
    setIsTracking(true);
    setStartTime(now);
    setMovementCount(0);
    setElapsedTime(0);
    
    const newSession: MovementSession = {
      id: Date.now().toString(),
      date: now,
      startTime: now,
      movements: 0,
      duration: 0,
      week: pregnancyWeek
    };
    setCurrentSession(newSession);
  };

  // إيقاف جلسة التتبع
  const stopTracking = () => {
    if (currentSession && startTime) {
      const endTime = new Date();
      const duration = Math.floor((endTime.getTime() - startTime.getTime()) / (1000 * 60));
      
      const completedSession: MovementSession = {
        ...currentSession,
        endTime,
        movements: movementCount,
        duration
      };
      
      setSessions(prev => [completedSession, ...prev]);
      setIsTracking(false);
      setCurrentSession(null);
      setStartTime(null);
      setElapsedTime(0);
    }
  };

  // إضافة حركة
  const addMovement = () => {
    setMovementCount(prev => prev + 1);
    
    // إذا وصل إلى 10 حركات، أوقف التتبع تلقائياً
    if (movementCount + 1 >= 10) {
      setTimeout(stopTracking, 500);
    }
  };

  // إعادة تعيين الجلسة
  const resetSession = () => {
    setIsTracking(false);
    setCurrentSession(null);
    setMovementCount(0);
    setStartTime(null);
    setElapsedTime(0);
  };

  // تنسيق الوقت
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // تحليل البيانات
  const getAverageMovements = () => {
    if (sessions.length === 0) return 0;
    const total = sessions.reduce((sum, session) => sum + session.movements, 0);
    return (total / sessions.length).toFixed(1);
  };

  const getAverageDuration = () => {
    if (sessions.length === 0) return 0;
    const total = sessions.reduce((sum, session) => sum + session.duration, 0);
    return (total / sessions.length).toFixed(1);
  };

  // تحديد ما إذا كان الأسبوع مناسب لتتبع الحركة
  const isTrackingRecommended = pregnancyWeek >= 18;

  const getMovementGuidelines = () => {
    if (pregnancyWeek < 18) {
      return {
        message: 'قد تبدئين بالشعور بحركة الجنين قريباً',
        color: 'blue',
        icon: Baby
      };
    } else if (pregnancyWeek < 28) {
      return {
        message: 'راقبي حركة الجنين يومياً - الهدف 10 حركات في ساعتين',
        color: 'green',
        icon: TrendingUp
      };
    } else {
      return {
        message: 'مراقبة يومية مهمة - اتصلي بالطبيب إذا قلت الحركة',
        color: 'orange',
        icon: AlertTriangle
      };
    }
  };

  const guidelines = getMovementGuidelines();

  return (
    <div className="space-y-6">
      {/* إرشادات الحركة */}
      <div className={`card bg-${guidelines.color}-50 border-${guidelines.color}-200`}>
        <div className="flex items-center">
          <guidelines.icon className={`w-8 h-8 text-${guidelines.color}-500 ml-3`} />
          <div>
            <h3 className="font-bold text-lg">تتبع حركة الجنين</h3>
            <p className={`text-${guidelines.color}-700`}>{guidelines.message}</p>
          </div>
        </div>
      </div>

      {isTrackingRecommended && (
        <>
          {/* جلسة التتبع الحالية */}
          <div className="card">
            <h3 className="text-xl font-bold mb-6 flex items-center">
              <Baby className="w-6 h-6 ml-2 text-pink-500" />
              جلسة تتبع الحركة
            </h3>

            {!isTracking ? (
              <div className="text-center py-8">
                <motion.button
                  onClick={startTracking}
                  className="btn-primary text-xl py-4 px-8"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Play className="w-6 h-6 inline ml-2" />
                  بدء تتبع الحركة
                </motion.button>
                <p className="text-gray-600 mt-4">
                  اجلسي في مكان هادئ وابدئي بتتبع حركة جنينك
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* العداد والوقت */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-6 bg-gradient-to-br from-pink-100 to-purple-100 rounded-xl">
                    <h4 className="font-semibold text-gray-800 mb-2">عدد الحركات</h4>
                    <motion.div
                      key={movementCount}
                      initial={{ scale: 1.2 }}
                      animate={{ scale: 1 }}
                      className="text-4xl font-bold text-pink-600"
                    >
                      {movementCount}
                    </motion.div>
                    <p className="text-sm text-gray-600">من 10</p>
                  </div>
                  
                  <div className="text-center p-6 bg-gradient-to-br from-blue-100 to-cyan-100 rounded-xl">
                    <h4 className="font-semibold text-gray-800 mb-2">الوقت المنقضي</h4>
                    <div className="text-4xl font-bold text-blue-600 font-mono">
                      {formatTime(elapsedTime)}
                    </div>
                    <p className="text-sm text-gray-600">دقيقة:ثانية</p>
                  </div>
                </div>

                {/* أزرار التحكم */}
                <div className="flex justify-center space-x-4 space-x-reverse">
                  <motion.button
                    onClick={addMovement}
                    className="btn-primary text-lg py-3 px-8"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    disabled={movementCount >= 10}
                  >
                    <Baby className="w-5 h-5 inline ml-2" />
                    حركة!
                  </motion.button>
                  
                  <button
                    onClick={stopTracking}
                    className="btn-secondary flex items-center"
                  >
                    <Pause className="w-4 h-4 ml-1" />
                    إيقاف
                  </button>
                  
                  <button
                    onClick={resetSession}
                    className="btn-secondary flex items-center"
                  >
                    <RotateCcw className="w-4 h-4 ml-1" />
                    إعادة تعيين
                  </button>
                </div>

                {/* شريط التقدم */}
                <div className="w-full bg-gray-200 rounded-full h-4">
                  <motion.div
                    className="bg-gradient-to-r from-pink-400 to-purple-500 h-4 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${(movementCount / 10) * 100}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
                
                {movementCount >= 10 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center p-4 bg-green-100 rounded-xl"
                  >
                    <h4 className="font-bold text-green-800 mb-2">ممتاز! 🎉</h4>
                    <p className="text-green-700">
                      تم تسجيل 10 حركات في {formatTime(elapsedTime)}
                    </p>
                  </motion.div>
                )}
              </div>
            )}
          </div>

          {/* الإحصائيات */}
          {sessions.length > 0 && (
            <div className="card">
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <TrendingUp className="w-6 h-6 ml-2 text-green-500" />
                إحصائيات الحركة
              </h3>
              
              <div className="grid md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-blue-50 rounded-xl">
                  <h4 className="font-semibold text-blue-800">متوسط الحركات</h4>
                  <p className="text-2xl font-bold text-blue-600">{getAverageMovements()}</p>
                  <p className="text-sm text-blue-600">حركة/جلسة</p>
                </div>
                
                <div className="text-center p-4 bg-green-50 rounded-xl">
                  <h4 className="font-semibold text-green-800">متوسط المدة</h4>
                  <p className="text-2xl font-bold text-green-600">{getAverageDuration()}</p>
                  <p className="text-sm text-green-600">دقيقة</p>
                </div>
                
                <div className="text-center p-4 bg-purple-50 rounded-xl">
                  <h4 className="font-semibold text-purple-800">عدد الجلسات</h4>
                  <p className="text-2xl font-bold text-purple-600">{sessions.length}</p>
                  <p className="text-sm text-purple-600">جلسة مسجلة</p>
                </div>
              </div>

              {/* آخر الجلسات */}
              <div>
                <h4 className="font-semibold mb-3">آخر الجلسات</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {sessions.slice(0, 5).map((session) => (
                    <div key={session.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <div>
                        <span className="font-medium">{session.movements} حركات</span>
                        <span className="text-sm text-gray-600 mr-2">
                          في {session.duration} دقيقة
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">
                        {session.date.toLocaleDateString('ar-SA')} - 
                        {session.date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
