'use client';

import { useState, useEffect } from 'react';
import { Apple, Droplets, Pill, AlertTriangle, CheckCircle, TrendingUp } from 'lucide-react';
import { motion } from 'framer-motion';

interface NutritionEntry {
  id: string;
  date: Date;
  water: number; // أكواب
  fruits: number; // حصص
  vegetables: number; // حصص
  protein: number; // حصص
  dairy: number; // حصص
  grains: number; // حصص
  supplements: string[]; // المكملات المتناولة
}

interface SmartNutritionProps {
  pregnancyWeek: number;
  trimester: 1 | 2 | 3;
}

export default function SmartNutrition({ pregnancyWeek, trimester }: SmartNutritionProps) {
  const [entries, setEntries] = useState<NutritionEntry[]>([]);
  const [todayEntry, setTodayEntry] = useState<NutritionEntry>({
    id: '',
    date: new Date(),
    water: 0,
    fruits: 0,
    vegetables: 0,
    protein: 0,
    dairy: 0,
    grains: 0,
    supplements: []
  });

  // الاحتياجات الغذائية حسب الثلث
  const getNutritionalNeeds = () => {
    const baseNeeds = {
      water: 8, // أكواب
      fruits: 3, // حصص
      vegetables: 4, // حصص
      protein: 3, // حصص
      dairy: 3, // حصص
      grains: 6, // حصص
      calories: 1800
    };

    if (trimester === 1) {
      return { ...baseNeeds, calories: 1800 };
    } else if (trimester === 2) {
      return { ...baseNeeds, calories: 2200, protein: 4 };
    } else {
      return { ...baseNeeds, calories: 2400, protein: 4, dairy: 4 };
    }
  };

  const needs = getNutritionalNeeds();

  // المكملات الموصى بها
  const getRecommendedSupplements = () => {
    const base = ['حمض الفوليك', 'فيتامينات الحمل'];
    
    if (trimester >= 2) {
      base.push('الحديد', 'الكالسيوم');
    }
    
    if (trimester === 3) {
      base.push('فيتامين د', 'أوميجا 3');
    }
    
    return base;
  };

  // تحديث القيم
  const updateValue = (field: keyof NutritionEntry, value: number | string[]) => {
    setTodayEntry(prev => ({ ...prev, [field]: value }));
  };

  // حفظ اليوم
  const saveToday = () => {
    const today = new Date().toDateString();
    const existingIndex = entries.findIndex(entry => 
      entry.date.toDateString() === today
    );

    const updatedEntry = {
      ...todayEntry,
      id: existingIndex >= 0 ? entries[existingIndex].id : Date.now().toString(),
      date: new Date()
    };

    if (existingIndex >= 0) {
      setEntries(prev => prev.map((entry, index) => 
        index === existingIndex ? updatedEntry : entry
      ));
    } else {
      setEntries(prev => [updatedEntry, ...prev]);
    }
  };

  // تحميل بيانات اليوم
  useEffect(() => {
    const today = new Date().toDateString();
    const todayData = entries.find(entry => 
      entry.date.toDateString() === today
    );
    
    if (todayData) {
      setTodayEntry(todayData);
    }
  }, [entries]);

  // حساب النسبة المئوية للإنجاز
  const getCompletionPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  // تحليل التغذية
  const analyzeNutrition = () => {
    const scores = {
      water: getCompletionPercentage(todayEntry.water, needs.water),
      fruits: getCompletionPercentage(todayEntry.fruits, needs.fruits),
      vegetables: getCompletionPercentage(todayEntry.vegetables, needs.vegetables),
      protein: getCompletionPercentage(todayEntry.protein, needs.protein),
      dairy: getCompletionPercentage(todayEntry.dairy, needs.dairy),
      grains: getCompletionPercentage(todayEntry.grains, needs.grains)
    };

    const averageScore = Object.values(scores).reduce((sum, score) => sum + score, 0) / 6;
    
    if (averageScore >= 80) {
      return { status: 'excellent', message: 'تغذية ممتازة! استمري هكذا', color: 'green' };
    } else if (averageScore >= 60) {
      return { status: 'good', message: 'تغذية جيدة، يمكن تحسينها', color: 'blue' };
    } else {
      return { status: 'needs-improvement', message: 'تحتاجين لتحسين تغذيتك', color: 'orange' };
    }
  };

  const nutritionAnalysis = analyzeNutrition();
  const recommendedSupplements = getRecommendedSupplements();

  // الأطعمة الموصى بها حسب الثلث
  const getRecommendedFoods = () => {
    const base = {
      fruits: ['التفاح', 'الموز', 'البرتقال', 'التوت'],
      vegetables: ['السبانخ', 'البروكلي', 'الجزر', 'الطماطم'],
      protein: ['الدجاج', 'السمك', 'البيض', 'البقوليات'],
      dairy: ['الحليب', 'الزبادي', 'الجبن', 'اللبنة'],
      grains: ['الأرز البني', 'الخبز الكامل', 'الشوفان', 'الكينوا']
    };

    if (trimester === 1) {
      base.fruits.push('الزنجبيل لتخفيف الغثيان');
    } else if (trimester === 2) {
      base.protein.push('اللحوم الحمراء للحديد');
    } else {
      base.dairy.push('السردين للكالسيوم');
    }

    return base;
  };

  const recommendedFoods = getRecommendedFoods();

  return (
    <div className="space-y-6">
      {/* تحليل التغذية */}
      <div className={`card bg-${nutritionAnalysis.color}-50 border-${nutritionAnalysis.color}-200`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {nutritionAnalysis.status === 'excellent' ? (
              <CheckCircle className={`w-8 h-8 text-${nutritionAnalysis.color}-500 ml-3`} />
            ) : (
              <TrendingUp className={`w-8 h-8 text-${nutritionAnalysis.color}-500 ml-3`} />
            )}
            <div>
              <h3 className="font-bold text-lg">تحليل التغذية اليومية</h3>
              <p className={`text-${nutritionAnalysis.color}-700`}>{nutritionAnalysis.message}</p>
            </div>
          </div>
          <button
            onClick={saveToday}
            className="btn-primary"
          >
            حفظ اليوم
          </button>
        </div>
      </div>

      {/* متتبع التغذية اليومية */}
      <div className="card">
        <h3 className="text-xl font-bold mb-6 flex items-center">
          <Apple className="w-6 h-6 ml-2 text-green-500" />
          متتبع التغذية اليومية
        </h3>

        <div className="grid md:grid-cols-2 gap-6">
          {/* الماء */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Droplets className="w-5 h-5 text-blue-500 ml-2" />
                <span className="font-medium">الماء</span>
              </div>
              <span className="text-sm text-gray-600">{todayEntry.water}/{needs.water} أكواب</span>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={() => updateValue('water', Math.max(0, todayEntry.water - 1))}
                className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center"
              >
                -
              </button>
              <div className="flex-1 bg-gray-200 rounded-full h-3">
                <div
                  className="bg-blue-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${getCompletionPercentage(todayEntry.water, needs.water)}%` }}
                />
              </div>
              <button
                onClick={() => updateValue('water', todayEntry.water + 1)}
                className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center"
              >
                +
              </button>
            </div>
          </div>

          {/* الفواكه */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="font-medium">الفواكه</span>
              <span className="text-sm text-gray-600">{todayEntry.fruits}/{needs.fruits} حصص</span>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={() => updateValue('fruits', Math.max(0, todayEntry.fruits - 1))}
                className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center"
              >
                -
              </button>
              <div className="flex-1 bg-gray-200 rounded-full h-3">
                <div
                  className="bg-orange-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${getCompletionPercentage(todayEntry.fruits, needs.fruits)}%` }}
                />
              </div>
              <button
                onClick={() => updateValue('fruits', todayEntry.fruits + 1)}
                className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center"
              >
                +
              </button>
            </div>
          </div>

          {/* الخضروات */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="font-medium">الخضروات</span>
              <span className="text-sm text-gray-600">{todayEntry.vegetables}/{needs.vegetables} حصص</span>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={() => updateValue('vegetables', Math.max(0, todayEntry.vegetables - 1))}
                className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center"
              >
                -
              </button>
              <div className="flex-1 bg-gray-200 rounded-full h-3">
                <div
                  className="bg-green-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${getCompletionPercentage(todayEntry.vegetables, needs.vegetables)}%` }}
                />
              </div>
              <button
                onClick={() => updateValue('vegetables', todayEntry.vegetables + 1)}
                className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center"
              >
                +
              </button>
            </div>
          </div>

          {/* البروتين */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="font-medium">البروتين</span>
              <span className="text-sm text-gray-600">{todayEntry.protein}/{needs.protein} حصص</span>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={() => updateValue('protein', Math.max(0, todayEntry.protein - 1))}
                className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center"
              >
                -
              </button>
              <div className="flex-1 bg-gray-200 rounded-full h-3">
                <div
                  className="bg-red-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${getCompletionPercentage(todayEntry.protein, needs.protein)}%` }}
                />
              </div>
              <button
                onClick={() => updateValue('protein', todayEntry.protein + 1)}
                className="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center"
              >
                +
              </button>
            </div>
          </div>

          {/* منتجات الألبان */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="font-medium">منتجات الألبان</span>
              <span className="text-sm text-gray-600">{todayEntry.dairy}/{needs.dairy} حصص</span>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={() => updateValue('dairy', Math.max(0, todayEntry.dairy - 1))}
                className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center"
              >
                -
              </button>
              <div className="flex-1 bg-gray-200 rounded-full h-3">
                <div
                  className="bg-purple-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${getCompletionPercentage(todayEntry.dairy, needs.dairy)}%` }}
                />
              </div>
              <button
                onClick={() => updateValue('dairy', todayEntry.dairy + 1)}
                className="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center"
              >
                +
              </button>
            </div>
          </div>

          {/* الحبوب */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="font-medium">الحبوب الكاملة</span>
              <span className="text-sm text-gray-600">{todayEntry.grains}/{needs.grains} حصص</span>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={() => updateValue('grains', Math.max(0, todayEntry.grains - 1))}
                className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center"
              >
                -
              </button>
              <div className="flex-1 bg-gray-200 rounded-full h-3">
                <div
                  className="bg-yellow-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${getCompletionPercentage(todayEntry.grains, needs.grains)}%` }}
                />
              </div>
              <button
                onClick={() => updateValue('grains', todayEntry.grains + 1)}
                className="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center"
              >
                +
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* المكملات الغذائية */}
      <div className="card">
        <h3 className="text-xl font-bold mb-4 flex items-center">
          <Pill className="w-6 h-6 ml-2 text-purple-500" />
          المكملات الغذائية
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {recommendedSupplements.map((supplement) => (
            <label key={supplement} className="flex items-center space-x-2 space-x-reverse">
              <input
                type="checkbox"
                checked={todayEntry.supplements.includes(supplement)}
                onChange={(e) => {
                  if (e.target.checked) {
                    updateValue('supplements', [...todayEntry.supplements, supplement]);
                  } else {
                    updateValue('supplements', todayEntry.supplements.filter(s => s !== supplement));
                  }
                }}
                className="rounded"
              />
              <span className="text-sm">{supplement}</span>
            </label>
          ))}
        </div>
      </div>

      {/* الأطعمة الموصى بها */}
      <div className="card">
        <h3 className="text-xl font-bold mb-4">الأطعمة الموصى بها للثلث {trimester}</h3>
        <div className="grid md:grid-cols-2 gap-6">
          {Object.entries(recommendedFoods).map(([category, foods]) => (
            <div key={category}>
              <h4 className="font-semibold mb-2 capitalize">{
                category === 'fruits' ? 'الفواكه' :
                category === 'vegetables' ? 'الخضروات' :
                category === 'protein' ? 'البروتين' :
                category === 'dairy' ? 'منتجات الألبان' :
                'الحبوب'
              }</h4>
              <div className="flex flex-wrap gap-2">
                {foods.map((food, index) => (
                  <span key={index} className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                    {food}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
