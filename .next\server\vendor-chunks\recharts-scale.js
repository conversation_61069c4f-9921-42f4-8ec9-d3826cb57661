"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/recharts-scale";
exports.ids = ["vendor-chunks/recharts-scale"];
exports.modules = {

/***/ "(ssr)/./node_modules/recharts-scale/lib/getNiceTickValues.js":
/*!**************************************************************!*\
  !*** ./node_modules/recharts-scale/lib/getNiceTickValues.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.getTickValuesFixedDomain = exports.getTickValues = exports.getNiceTickValues = void 0;\n\nvar _decimal = _interopRequireDefault(__webpack_require__(/*! decimal.js-light */ \"(ssr)/./node_modules/decimal.js-light/decimal.js\"));\n\nvar _utils = __webpack_require__(/*! ./util/utils */ \"(ssr)/./node_modules/recharts-scale/lib/util/utils.js\");\n\nvar _arithmetic = _interopRequireDefault(__webpack_require__(/*! ./util/arithmetic */ \"(ssr)/./node_modules/recharts-scale/lib/util/arithmetic.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return; var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\n/**\n * Calculate a interval of a minimum value and a maximum value\n *\n * @param  {Number} min       The minimum value\n * @param  {Number} max       The maximum value\n * @return {Array} An interval\n */\nfunction getValidInterval(_ref) {\n  var _ref2 = _slicedToArray(_ref, 2),\n      min = _ref2[0],\n      max = _ref2[1];\n\n  var validMin = min,\n      validMax = max; // exchange\n\n  if (min > max) {\n    validMin = max;\n    validMax = min;\n  }\n\n  return [validMin, validMax];\n}\n/**\n * Calculate the step which is easy to understand between ticks, like 10, 20, 25\n *\n * @param  {Decimal} roughStep        The rough step calculated by deviding the\n * difference by the tickCount\n * @param  {Boolean} allowDecimals    Allow the ticks to be decimals or not\n * @param  {Integer} correctionFactor A correction factor\n * @return {Decimal} The step which is easy to understand between two ticks\n */\n\n\nfunction getFormatStep(roughStep, allowDecimals, correctionFactor) {\n  if (roughStep.lte(0)) {\n    return new _decimal.default(0);\n  }\n\n  var digitCount = _arithmetic.default.getDigitCount(roughStep.toNumber()); // The ratio between the rough step and the smallest number which has a bigger\n  // order of magnitudes than the rough step\n\n\n  var digitCountValue = new _decimal.default(10).pow(digitCount);\n  var stepRatio = roughStep.div(digitCountValue); // When an integer and a float multiplied, the accuracy of result may be wrong\n\n  var stepRatioScale = digitCount !== 1 ? 0.05 : 0.1;\n  var amendStepRatio = new _decimal.default(Math.ceil(stepRatio.div(stepRatioScale).toNumber())).add(correctionFactor).mul(stepRatioScale);\n  var formatStep = amendStepRatio.mul(digitCountValue);\n  return allowDecimals ? formatStep : new _decimal.default(Math.ceil(formatStep));\n}\n/**\n * calculate the ticks when the minimum value equals to the maximum value\n *\n * @param  {Number}  value         The minimum valuue which is also the maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}                 ticks\n */\n\n\nfunction getTickOfSingleValue(value, tickCount, allowDecimals) {\n  var step = 1; // calculate the middle value of ticks\n\n  var middle = new _decimal.default(value);\n\n  if (!middle.isint() && allowDecimals) {\n    var absVal = Math.abs(value);\n\n    if (absVal < 1) {\n      // The step should be a float number when the difference is smaller than 1\n      step = new _decimal.default(10).pow(_arithmetic.default.getDigitCount(value) - 1);\n      middle = new _decimal.default(Math.floor(middle.div(step).toNumber())).mul(step);\n    } else if (absVal > 1) {\n      // Return the maximum integer which is smaller than 'value' when 'value' is greater than 1\n      middle = new _decimal.default(Math.floor(value));\n    }\n  } else if (value === 0) {\n    middle = new _decimal.default(Math.floor((tickCount - 1) / 2));\n  } else if (!allowDecimals) {\n    middle = new _decimal.default(Math.floor(value));\n  }\n\n  var middleIndex = Math.floor((tickCount - 1) / 2);\n  var fn = (0, _utils.compose)((0, _utils.map)(function (n) {\n    return middle.add(new _decimal.default(n - middleIndex).mul(step)).toNumber();\n  }), _utils.range);\n  return fn(0, tickCount);\n}\n/**\n * Calculate the step\n *\n * @param  {Number}  min              The minimum value of an interval\n * @param  {Number}  max              The maximum value of an interval\n * @param  {Integer} tickCount        The count of ticks\n * @param  {Boolean} allowDecimals    Allow the ticks to be decimals or not\n * @param  {Number}  correctionFactor A correction factor\n * @return {Object}  The step, minimum value of ticks, maximum value of ticks\n */\n\n\nfunction calculateStep(min, max, tickCount, allowDecimals) {\n  var correctionFactor = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;\n\n  // dirty hack (for recharts' test)\n  if (!Number.isFinite((max - min) / (tickCount - 1))) {\n    return {\n      step: new _decimal.default(0),\n      tickMin: new _decimal.default(0),\n      tickMax: new _decimal.default(0)\n    };\n  } // The step which is easy to understand between two ticks\n\n\n  var step = getFormatStep(new _decimal.default(max).sub(min).div(tickCount - 1), allowDecimals, correctionFactor); // A medial value of ticks\n\n  var middle; // When 0 is inside the interval, 0 should be a tick\n\n  if (min <= 0 && max >= 0) {\n    middle = new _decimal.default(0);\n  } else {\n    // calculate the middle value\n    middle = new _decimal.default(min).add(max).div(2); // minus modulo value\n\n    middle = middle.sub(new _decimal.default(middle).mod(step));\n  }\n\n  var belowCount = Math.ceil(middle.sub(min).div(step).toNumber());\n  var upCount = Math.ceil(new _decimal.default(max).sub(middle).div(step).toNumber());\n  var scaleCount = belowCount + upCount + 1;\n\n  if (scaleCount > tickCount) {\n    // When more ticks need to cover the interval, step should be bigger.\n    return calculateStep(min, max, tickCount, allowDecimals, correctionFactor + 1);\n  }\n\n  if (scaleCount < tickCount) {\n    // When less ticks can cover the interval, we should add some additional ticks\n    upCount = max > 0 ? upCount + (tickCount - scaleCount) : upCount;\n    belowCount = max > 0 ? belowCount : belowCount + (tickCount - scaleCount);\n  }\n\n  return {\n    step: step,\n    tickMin: middle.sub(new _decimal.default(belowCount).mul(step)),\n    tickMax: middle.add(new _decimal.default(upCount).mul(step))\n  };\n}\n/**\n * Calculate the ticks of an interval, the count of ticks will be guraranteed\n *\n * @param  {Number}  min, max      min: The minimum value, max: The maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}   ticks\n */\n\n\nfunction getNiceTickValuesFn(_ref3) {\n  var _ref4 = _slicedToArray(_ref3, 2),\n      min = _ref4[0],\n      max = _ref4[1];\n\n  var tickCount = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 6;\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  // More than two ticks should be return\n  var count = Math.max(tickCount, 2);\n\n  var _getValidInterval = getValidInterval([min, max]),\n      _getValidInterval2 = _slicedToArray(_getValidInterval, 2),\n      cormin = _getValidInterval2[0],\n      cormax = _getValidInterval2[1];\n\n  if (cormin === -Infinity || cormax === Infinity) {\n    var _values = cormax === Infinity ? [cormin].concat(_toConsumableArray((0, _utils.range)(0, tickCount - 1).map(function () {\n      return Infinity;\n    }))) : [].concat(_toConsumableArray((0, _utils.range)(0, tickCount - 1).map(function () {\n      return -Infinity;\n    })), [cormax]);\n\n    return min > max ? (0, _utils.reverse)(_values) : _values;\n  }\n\n  if (cormin === cormax) {\n    return getTickOfSingleValue(cormin, tickCount, allowDecimals);\n  } // Get the step between two ticks\n\n\n  var _calculateStep = calculateStep(cormin, cormax, count, allowDecimals),\n      step = _calculateStep.step,\n      tickMin = _calculateStep.tickMin,\n      tickMax = _calculateStep.tickMax;\n\n  var values = _arithmetic.default.rangeStep(tickMin, tickMax.add(new _decimal.default(0.1).mul(step)), step);\n\n  return min > max ? (0, _utils.reverse)(values) : values;\n}\n/**\n * Calculate the ticks of an interval, the count of ticks won't be guraranteed\n *\n * @param  {Number}  min, max      min: The minimum value, max: The maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}   ticks\n */\n\n\nfunction getTickValuesFn(_ref5) {\n  var _ref6 = _slicedToArray(_ref5, 2),\n      min = _ref6[0],\n      max = _ref6[1];\n\n  var tickCount = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 6;\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  // More than two ticks should be return\n  var count = Math.max(tickCount, 2);\n\n  var _getValidInterval3 = getValidInterval([min, max]),\n      _getValidInterval4 = _slicedToArray(_getValidInterval3, 2),\n      cormin = _getValidInterval4[0],\n      cormax = _getValidInterval4[1];\n\n  if (cormin === -Infinity || cormax === Infinity) {\n    return [min, max];\n  }\n\n  if (cormin === cormax) {\n    return getTickOfSingleValue(cormin, tickCount, allowDecimals);\n  }\n\n  var step = getFormatStep(new _decimal.default(cormax).sub(cormin).div(count - 1), allowDecimals, 0);\n  var fn = (0, _utils.compose)((0, _utils.map)(function (n) {\n    return new _decimal.default(cormin).add(new _decimal.default(n).mul(step)).toNumber();\n  }), _utils.range);\n  var values = fn(0, count).filter(function (entry) {\n    return entry >= cormin && entry <= cormax;\n  });\n  return min > max ? (0, _utils.reverse)(values) : values;\n}\n/**\n * Calculate the ticks of an interval, the count of ticks won't be guraranteed,\n * but the domain will be guaranteed\n *\n * @param  {Number}  min, max      min: The minimum value, max: The maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}   ticks\n */\n\n\nfunction getTickValuesFixedDomainFn(_ref7, tickCount) {\n  var _ref8 = _slicedToArray(_ref7, 2),\n      min = _ref8[0],\n      max = _ref8[1];\n\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n\n  // More than two ticks should be return\n  var _getValidInterval5 = getValidInterval([min, max]),\n      _getValidInterval6 = _slicedToArray(_getValidInterval5, 2),\n      cormin = _getValidInterval6[0],\n      cormax = _getValidInterval6[1];\n\n  if (cormin === -Infinity || cormax === Infinity) {\n    return [min, max];\n  }\n\n  if (cormin === cormax) {\n    return [cormin];\n  }\n\n  var count = Math.max(tickCount, 2);\n  var step = getFormatStep(new _decimal.default(cormax).sub(cormin).div(count - 1), allowDecimals, 0);\n  var values = [].concat(_toConsumableArray(_arithmetic.default.rangeStep(new _decimal.default(cormin), new _decimal.default(cormax).sub(new _decimal.default(0.99).mul(step)), step)), [cormax]);\n  return min > max ? (0, _utils.reverse)(values) : values;\n}\n\nvar getNiceTickValues = (0, _utils.memoize)(getNiceTickValuesFn);\nexports.getNiceTickValues = getNiceTickValues;\nvar getTickValues = (0, _utils.memoize)(getTickValuesFn);\nexports.getTickValues = getTickValues;\nvar getTickValuesFixedDomain = (0, _utils.memoize)(getTickValuesFixedDomainFn);\nexports.getTickValuesFixedDomain = getTickValuesFixedDomain;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recharts-scale/lib/getNiceTickValues.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recharts-scale/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/recharts-scale/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"getTickValues\", ({\n  enumerable: true,\n  get: function get() {\n    return _getNiceTickValues.getTickValues;\n  }\n}));\nObject.defineProperty(exports, \"getNiceTickValues\", ({\n  enumerable: true,\n  get: function get() {\n    return _getNiceTickValues.getNiceTickValues;\n  }\n}));\nObject.defineProperty(exports, \"getTickValuesFixedDomain\", ({\n  enumerable: true,\n  get: function get() {\n    return _getNiceTickValues.getTickValuesFixedDomain;\n  }\n}));\n\nvar _getNiceTickValues = __webpack_require__(/*! ./getNiceTickValues */ \"(ssr)/./node_modules/recharts-scale/lib/getNiceTickValues.js\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjaGFydHMtc2NhbGUvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGlEQUFnRDtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLHFEQUFvRDtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLDREQUEyRDtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQzs7QUFFRix5QkFBeUIsbUJBQU8sQ0FBQyx5RkFBcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVnbmFuY3ktY2FsY3VsYXRvci8uL25vZGVfbW9kdWxlcy9yZWNoYXJ0cy1zY2FsZS9saWIvaW5kZXguanM/YWVmNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImdldFRpY2tWYWx1ZXNcIiwge1xuICBlbnVtZXJhYmxlOiB0cnVlLFxuICBnZXQ6IGZ1bmN0aW9uIGdldCgpIHtcbiAgICByZXR1cm4gX2dldE5pY2VUaWNrVmFsdWVzLmdldFRpY2tWYWx1ZXM7XG4gIH1cbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiZ2V0TmljZVRpY2tWYWx1ZXNcIiwge1xuICBlbnVtZXJhYmxlOiB0cnVlLFxuICBnZXQ6IGZ1bmN0aW9uIGdldCgpIHtcbiAgICByZXR1cm4gX2dldE5pY2VUaWNrVmFsdWVzLmdldE5pY2VUaWNrVmFsdWVzO1xuICB9XG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImdldFRpY2tWYWx1ZXNGaXhlZERvbWFpblwiLCB7XG4gIGVudW1lcmFibGU6IHRydWUsXG4gIGdldDogZnVuY3Rpb24gZ2V0KCkge1xuICAgIHJldHVybiBfZ2V0TmljZVRpY2tWYWx1ZXMuZ2V0VGlja1ZhbHVlc0ZpeGVkRG9tYWluO1xuICB9XG59KTtcblxudmFyIF9nZXROaWNlVGlja1ZhbHVlcyA9IHJlcXVpcmUoXCIuL2dldE5pY2VUaWNrVmFsdWVzXCIpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recharts-scale/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recharts-scale/lib/util/arithmetic.js":
/*!************************************************************!*\
  !*** ./node_modules/recharts-scale/lib/util/arithmetic.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\n\nvar _decimal = _interopRequireDefault(__webpack_require__(/*! decimal.js-light */ \"(ssr)/./node_modules/decimal.js-light/decimal.js\"));\n\nvar _utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/recharts-scale/lib/util/utils.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @fileOverview 一些公用的运算方法\n * <AUTHOR> * @date 2015-09-17\n */\n\n/**\n * 获取数值的位数\n * 其中绝对值属于区间[0.1, 1)， 得到的值为0\n * 绝对值属于区间[0.01, 0.1)，得到的位数为 -1\n * 绝对值属于区间[0.001, 0.01)，得到的位数为 -2\n *\n * @param  {Number} value 数值\n * @return {Integer} 位数\n */\nfunction getDigitCount(value) {\n  var result;\n\n  if (value === 0) {\n    result = 1;\n  } else {\n    result = Math.floor(new _decimal.default(value).abs().log(10).toNumber()) + 1;\n  }\n\n  return result;\n}\n/**\n * 按照固定的步长获取[start, end)这个区间的数据\n * 并且需要处理js计算精度的问题\n *\n * @param  {Decimal} start 起点\n * @param  {Decimal} end   终点，不包含该值\n * @param  {Decimal} step  步长\n * @return {Array}         若干数值\n */\n\n\nfunction rangeStep(start, end, step) {\n  var num = new _decimal.default(start);\n  var i = 0;\n  var result = []; // magic number to prevent infinite loop\n\n  while (num.lt(end) && i < 100000) {\n    result.push(num.toNumber());\n    num = num.add(step);\n    i++;\n  }\n\n  return result;\n}\n/**\n * 对数值进行线性插值\n *\n * @param  {Number} a  定义域的极点\n * @param  {Number} b  定义域的极点\n * @param  {Number} t  [0, 1]内的某个值\n * @return {Number}    定义域内的某个值\n */\n\n\nvar interpolateNumber = (0, _utils.curry)(function (a, b, t) {\n  var newA = +a;\n  var newB = +b;\n  return newA + t * (newB - newA);\n});\n/**\n * 线性插值的逆运算\n *\n * @param  {Number} a 定义域的极点\n * @param  {Number} b 定义域的极点\n * @param  {Number} x 可以认为是插值后的一个输出值\n * @return {Number}   当x在 a ~ b这个范围内时，返回值属于[0, 1]\n */\n\nvar uninterpolateNumber = (0, _utils.curry)(function (a, b, x) {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return (x - a) / diff;\n});\n/**\n * 线性插值的逆运算，并且有截断的操作\n *\n * @param  {Number} a 定义域的极点\n * @param  {Number} b 定义域的极点\n * @param  {Number} x 可以认为是插值后的一个输出值\n * @return {Number}   当x在 a ~ b这个区间内时，返回值属于[0, 1]，\n * 当x不在 a ~ b这个区间时，会截断到 a ~ b 这个区间\n */\n\nvar uninterpolateTruncation = (0, _utils.curry)(function (a, b, x) {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return Math.max(0, Math.min(1, (x - a) / diff));\n});\nvar _default = {\n  rangeStep: rangeStep,\n  getDigitCount: getDigitCount,\n  interpolateNumber: interpolateNumber,\n  uninterpolateNumber: uninterpolateNumber,\n  uninterpolateTruncation: uninterpolateTruncation\n};\nexports[\"default\"] = _default;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recharts-scale/lib/util/arithmetic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recharts-scale/lib/util/utils.js":
/*!*******************************************************!*\
  !*** ./node_modules/recharts-scale/lib/util/utils.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.memoize = exports.reverse = exports.compose = exports.map = exports.range = exports.curry = exports.PLACE_HOLDER = void 0;\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nvar identity = function identity(i) {\n  return i;\n};\n\nvar PLACE_HOLDER = {\n  '@@functional/placeholder': true\n};\nexports.PLACE_HOLDER = PLACE_HOLDER;\n\nvar isPlaceHolder = function isPlaceHolder(val) {\n  return val === PLACE_HOLDER;\n};\n\nvar curry0 = function curry0(fn) {\n  return function _curried() {\n    if (arguments.length === 0 || arguments.length === 1 && isPlaceHolder(arguments.length <= 0 ? undefined : arguments[0])) {\n      return _curried;\n    }\n\n    return fn.apply(void 0, arguments);\n  };\n};\n\nvar curryN = function curryN(n, fn) {\n  if (n === 1) {\n    return fn;\n  }\n\n  return curry0(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    var argsLength = args.filter(function (arg) {\n      return arg !== PLACE_HOLDER;\n    }).length;\n\n    if (argsLength >= n) {\n      return fn.apply(void 0, args);\n    }\n\n    return curryN(n - argsLength, curry0(function () {\n      for (var _len2 = arguments.length, restArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        restArgs[_key2] = arguments[_key2];\n      }\n\n      var newArgs = args.map(function (arg) {\n        return isPlaceHolder(arg) ? restArgs.shift() : arg;\n      });\n      return fn.apply(void 0, _toConsumableArray(newArgs).concat(restArgs));\n    }));\n  });\n};\n\nvar curry = function curry(fn) {\n  return curryN(fn.length, fn);\n};\n\nexports.curry = curry;\n\nvar range = function range(begin, end) {\n  var arr = [];\n\n  for (var i = begin; i < end; ++i) {\n    arr[i - begin] = i;\n  }\n\n  return arr;\n};\n\nexports.range = range;\nvar map = curry(function (fn, arr) {\n  if (Array.isArray(arr)) {\n    return arr.map(fn);\n  }\n\n  return Object.keys(arr).map(function (key) {\n    return arr[key];\n  }).map(fn);\n});\nexports.map = map;\n\nvar compose = function compose() {\n  for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    args[_key3] = arguments[_key3];\n  }\n\n  if (!args.length) {\n    return identity;\n  }\n\n  var fns = args.reverse(); // first function can receive multiply arguments\n\n  var firstFn = fns[0];\n  var tailsFn = fns.slice(1);\n  return function () {\n    return tailsFn.reduce(function (res, fn) {\n      return fn(res);\n    }, firstFn.apply(void 0, arguments));\n  };\n};\n\nexports.compose = compose;\n\nvar reverse = function reverse(arr) {\n  if (Array.isArray(arr)) {\n    return arr.reverse();\n  } // can be string\n\n\n  return arr.split('').reverse.join('');\n};\n\nexports.reverse = reverse;\n\nvar memoize = function memoize(fn) {\n  var lastArgs = null;\n  var lastResult = null;\n  return function () {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n\n    if (lastArgs && args.every(function (val, i) {\n      return val === lastArgs[i];\n    })) {\n      return lastResult;\n    }\n\n    lastArgs = args;\n    lastResult = fn.apply(void 0, args);\n    return lastResult;\n  };\n};\n\nexports.memoize = memoize;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recharts-scale/lib/util/utils.js\n");

/***/ })

};
;