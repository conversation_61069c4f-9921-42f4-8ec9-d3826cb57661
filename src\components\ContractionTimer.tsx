'use client';

import { useState, useEffect } from 'react';
import { Timer, Play, Pause, RotateCcw, AlertTriangle, Clock, TrendingUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface Contraction {
  id: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // بالثواني
  intensity: 1 | 2 | 3 | 4 | 5;
}

interface ContractionTimerProps {
  pregnancyWeek: number;
}

export default function ContractionTimer({ pregnancyWeek }: ContractionTimerProps) {
  const [contractions, setContractions] = useState<Contraction[]>([]);
  const [isTracking, setIsTracking] = useState(false);
  const [currentContraction, setCurrentContraction] = useState<Contraction | null>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [intensity, setIntensity] = useState<1 | 2 | 3 | 4 | 5>(1);

  // تحديث الوقت المنقضي
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTracking && startTime) {
      interval = setInterval(() => {
        setElapsedTime(Math.floor((new Date().getTime() - startTime.getTime()) / 1000));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTracking, startTime]);

  // بدء تتبع انقباض
  const startContraction = () => {
    const now = new Date();
    setIsTracking(true);
    setStartTime(now);
    setElapsedTime(0);
    
    const newContraction: Contraction = {
      id: Date.now().toString(),
      startTime: now,
      duration: 0,
      intensity: 1
    };
    setCurrentContraction(newContraction);
  };

  // إيقاف تتبع الانقباض
  const stopContraction = () => {
    if (currentContraction && startTime) {
      const endTime = new Date();
      const duration = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);
      
      const completedContraction: Contraction = {
        ...currentContraction,
        endTime,
        duration,
        intensity
      };
      
      setContractions(prev => [completedContraction, ...prev]);
      setIsTracking(false);
      setCurrentContraction(null);
      setStartTime(null);
      setElapsedTime(0);
      setIntensity(1);
    }
  };

  // إعادة تعيين
  const reset = () => {
    setIsTracking(false);
    setCurrentContraction(null);
    setStartTime(null);
    setElapsedTime(0);
    setIntensity(1);
  };

  // مسح جميع الانقباضات
  const clearAll = () => {
    setContractions([]);
    reset();
  };

  // تنسيق الوقت
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // حساب الفترة بين الانقباضات
  const getTimeBetweenContractions = () => {
    if (contractions.length < 2) return null;
    
    const intervals = [];
    for (let i = 0; i < contractions.length - 1; i++) {
      const interval = Math.floor(
        (contractions[i].startTime.getTime() - contractions[i + 1].startTime.getTime()) / (1000 * 60)
      );
      intervals.push(interval);
    }
    
    const average = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    return Math.round(average);
  };

  // حساب متوسط مدة الانقباضات
  const getAverageDuration = () => {
    if (contractions.length === 0) return 0;
    const total = contractions.reduce((sum, contraction) => sum + contraction.duration, 0);
    return Math.round(total / contractions.length);
  };

  // تحليل نمط الانقباضات
  const analyzePattern = () => {
    if (contractions.length < 3) return null;
    
    const recentContractions = contractions.slice(0, 5);
    const averageInterval = getTimeBetweenContractions();
    const averageDuration = getAverageDuration();
    
    if (averageInterval && averageInterval <= 5 && averageDuration >= 60) {
      return {
        type: 'labor',
        message: 'قد تكونين في مرحلة المخاض! اتصلي بطبيبك فوراً',
        color: 'red',
        urgent: true
      };
    } else if (averageInterval && averageInterval <= 10 && averageDuration >= 45) {
      return {
        type: 'early-labor',
        message: 'قد تكونين في بداية المخاض. راقبي الانقباضات واتصلي بطبيبك',
        color: 'orange',
        urgent: false
      };
    } else if (contractions.length >= 6) {
      return {
        type: 'braxton-hicks',
        message: 'قد تكون انقباضات براكستون هيكس (انقباضات تدريبية)',
        color: 'blue',
        urgent: false
      };
    }
    
    return null;
  };

  const pattern = analyzePattern();
  const timeBetween = getTimeBetweenContractions();
  const avgDuration = getAverageDuration();

  const intensityLabels = {
    1: 'خفيف جداً',
    2: 'خفيف',
    3: 'متوسط',
    4: 'قوي',
    5: 'قوي جداً'
  };

  const intensityColors = {
    1: 'bg-green-100 text-green-800',
    2: 'bg-yellow-100 text-yellow-800',
    3: 'bg-orange-100 text-orange-800',
    4: 'bg-red-100 text-red-800',
    5: 'bg-red-200 text-red-900'
  };

  return (
    <div className="space-y-6">
      {/* تحليل النمط */}
      {pattern && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`card bg-${pattern.color}-50 border-${pattern.color}-200`}
        >
          <div className="flex items-center">
            <AlertTriangle className={`w-8 h-8 text-${pattern.color}-500 ml-3`} />
            <div>
              <h3 className="font-bold text-lg">تحليل نمط الانقباضات</h3>
              <p className={`text-${pattern.color}-700 font-medium`}>{pattern.message}</p>
              {pattern.urgent && (
                <p className="text-red-600 font-bold mt-1">⚠️ اتصلي بطبيبك أو اذهبي للمستشفى فوراً</p>
              )}
            </div>
          </div>
        </motion.div>
      )}

      {/* مؤقت الانقباض */}
      <div className="card">
        <h3 className="text-xl font-bold mb-6 flex items-center">
          <Timer className="w-6 h-6 ml-2 text-blue-500" />
          مؤقت الانقباضات
        </h3>

        {!isTracking ? (
          <div className="text-center py-8">
            <motion.button
              onClick={startContraction}
              className="btn-primary text-xl py-4 px-8"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Play className="w-6 h-6 inline ml-2" />
              بدء انقباض
            </motion.button>
            <p className="text-gray-600 mt-4">
              اضغطي عند بداية الانقباض
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* العداد */}
            <div className="text-center p-8 bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl">
              <h4 className="font-semibold text-gray-800 mb-4">مدة الانقباض</h4>
              <motion.div
                key={elapsedTime}
                initial={{ scale: 1.1 }}
                animate={{ scale: 1 }}
                className="text-6xl font-bold text-blue-600 font-mono mb-4"
              >
                {formatTime(elapsedTime)}
              </motion.div>
              <div className="w-full bg-blue-200 rounded-full h-3">
                <motion.div
                  className="bg-blue-600 h-3 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${Math.min((elapsedTime / 120) * 100, 100)}%` }}
                />
              </div>
            </div>

            {/* شدة الانقباض */}
            <div>
              <h4 className="font-semibold mb-3">شدة الانقباض</h4>
              <div className="grid grid-cols-5 gap-2">
                {[1, 2, 3, 4, 5].map((level) => (
                  <button
                    key={level}
                    onClick={() => setIntensity(level as 1 | 2 | 3 | 4 | 5)}
                    className={`p-3 rounded-lg text-center transition-all ${
                      intensity === level
                        ? intensityColors[level as keyof typeof intensityColors]
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    <div className="font-bold">{level}</div>
                    <div className="text-xs">{intensityLabels[level as keyof typeof intensityLabels]}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* أزرار التحكم */}
            <div className="flex justify-center space-x-4 space-x-reverse">
              <motion.button
                onClick={stopContraction}
                className="btn-primary text-lg py-3 px-8"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Pause className="w-5 h-5 inline ml-2" />
                إنهاء الانقباض
              </motion.button>
              
              <button
                onClick={reset}
                className="btn-secondary flex items-center"
              >
                <RotateCcw className="w-4 h-4 ml-1" />
                إعادة تعيين
              </button>
            </div>
          </div>
        )}
      </div>

      {/* الإحصائيات */}
      {contractions.length > 0 && (
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold flex items-center">
              <TrendingUp className="w-6 h-6 ml-2 text-green-500" />
              إحصائيات الانقباضات
            </h3>
            <button
              onClick={clearAll}
              className="text-red-600 hover:text-red-700 text-sm"
            >
              مسح الكل
            </button>
          </div>
          
          <div className="grid md:grid-cols-3 gap-4 mb-6">
            <div className="text-center p-4 bg-blue-50 rounded-xl">
              <h4 className="font-semibold text-blue-800">متوسط المدة</h4>
              <p className="text-2xl font-bold text-blue-600">{avgDuration}ث</p>
            </div>
            
            {timeBetween && (
              <div className="text-center p-4 bg-green-50 rounded-xl">
                <h4 className="font-semibold text-green-800">الفترة بين الانقباضات</h4>
                <p className="text-2xl font-bold text-green-600">{timeBetween} دقيقة</p>
              </div>
            )}
            
            <div className="text-center p-4 bg-purple-50 rounded-xl">
              <h4 className="font-semibold text-purple-800">عدد الانقباضات</h4>
              <p className="text-2xl font-bold text-purple-600">{contractions.length}</p>
            </div>
          </div>

          {/* قائمة الانقباضات */}
          <div>
            <h4 className="font-semibold mb-3">سجل الانقباضات</h4>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {contractions.slice(0, 10).map((contraction, index) => (
                <div key={contraction.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <span className="font-medium ml-3">{formatTime(contraction.duration)}</span>
                    <span className={`px-2 py-1 rounded-full text-xs ${intensityColors[contraction.intensity]}`}>
                      {intensityLabels[contraction.intensity]}
                    </span>
                  </div>
                  <div className="text-sm text-gray-500">
                    {contraction.startTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}
                    {index < contractions.length - 1 && timeBetween && (
                      <span className="mr-2">
                        (فترة: {Math.round((contractions[index + 1].startTime.getTime() - contraction.startTime.getTime()) / (1000 * 60))} دقيقة)
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* معلومات مفيدة */}
      <div className="card bg-blue-50">
        <h4 className="font-bold text-blue-800 mb-3">متى تتصلين بالطبيب؟</h4>
        <ul className="text-blue-700 space-y-2 text-sm">
          <li>• انقباضات منتظمة كل 5 دقائق لمدة ساعة</li>
          <li>• انقباضات تستمر 60 ثانية أو أكثر</li>
          <li>• نزول الماء أو نزيف</li>
          <li>• ألم شديد أو تغير في حركة الجنين</li>
          <li>• أي قلق أو شك حول حالتك</li>
        </ul>
      </div>
    </div>
  );
}
