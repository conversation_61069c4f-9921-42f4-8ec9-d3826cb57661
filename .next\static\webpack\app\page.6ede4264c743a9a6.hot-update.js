"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PregnancyResults.tsx":
/*!*********************************************!*\
  !*** ./src/components/PregnancyResults.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PregnancyResults; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,Baby,BarChart3,Bell,Brain,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,Baby,BarChart3,Bell,Brain,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,Baby,BarChart3,Bell,Brain,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/baby.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,Baby,BarChart3,Bell,Brain,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,Baby,BarChart3,Bell,Brain,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,Baby,BarChart3,Bell,Brain,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,Baby,BarChart3,Bell,Brain,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,Baby,BarChart3,Bell,Brain,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,Baby,BarChart3,Bell,Brain,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,Baby,BarChart3,Bell,Brain,Calendar,Clock,Globe,Heart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/hijriCalendar */ \"(app-pages-browser)/./src/utils/hijriCalendar.ts\");\n/* harmony import */ var _utils_pregnancyCalculations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pregnancyCalculations */ \"(app-pages-browser)/./src/utils/pregnancyCalculations.ts\");\n/* harmony import */ var _PregnancyCharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PregnancyCharts */ \"(app-pages-browser)/./src/components/PregnancyCharts.tsx\");\n/* harmony import */ var _SymptomTracker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SymptomTracker */ \"(app-pages-browser)/./src/components/SymptomTracker.tsx\");\n/* harmony import */ var _SmartReminders__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./SmartReminders */ \"(app-pages-browser)/./src/components/SmartReminders.tsx\");\n/* harmony import */ var _HealthAnalytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./HealthAnalytics */ \"(app-pages-browser)/./src/components/HealthAnalytics.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PregnancyResults(param) {\n    let { results, onBack, lastMenstrualPeriod } = param;\n    _s();\n    const [calendarType, setCalendarType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"gregorian\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const hijriDueDate = (0,_utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__.gregorianToHijri)(results.dueDate);\n    const hijriOvulationDate = (0,_utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__.gregorianToHijri)(results.ovulationDate);\n    const trimesterInfo = (0,_utils_pregnancyCalculations__WEBPACK_IMPORTED_MODULE_3__.getTrimesterInfo)(results.trimester);\n    const weeklyTip = (0,_utils_pregnancyCalculations__WEBPACK_IMPORTED_MODULE_3__.getWeeklyTips)(results.currentWeeks);\n    const formatDate = (date)=>{\n        return calendarType === \"hijri\" ? (0,_utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__.formatHijriDate)((0,_utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__.gregorianToHijri)(date)) : (0,_utils_hijriCalendar__WEBPACK_IMPORTED_MODULE_2__.formatGregorianDate)(date);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-gradient relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full -translate-y-20 translate-x-20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 w-32 h-32 bg-white/10 rounded-full translate-y-16 -translate-x-16\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onBack,\n                                        className: \"flex items-center text-white/90 hover:text-white bg-white/20 backdrop-blur-sm px-4 py-2 rounded-xl transition-all duration-300 hover:bg-white/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"حساب جديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-white/80\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: calendarType,\n                                                onChange: (e)=>setCalendarType(e.target.value),\n                                                className: \"bg-white/20 backdrop-blur-sm text-white border border-white/30 rounded-xl px-4 py-2 text-sm font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"gregorian\",\n                                                        className: \"text-gray-800\",\n                                                        children: \"ميلادي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"hijri\",\n                                                        className: \"text-gray-800\",\n                                                        children: \"هجري\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 62,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-black text-white mb-6 leading-tight\",\n                                        children: \"نتائج حاسبة الحمل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-2xl border border-white/30 floating-animation\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-8 h-8 ml-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    results.currentWeeks,\n                                                    \" أسبوع و \",\n                                                    results.currentDays,\n                                                    \" أيام\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1 space-x-reverse overflow-x-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"overview\"),\n                            className: \"flex-1 py-3 px-4 rounded-lg font-medium transition-colors \".concat(activeTab === \"overview\" ? \"bg-primary-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-4 h-4 inline ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                \"نظرة عامة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"charts\"),\n                            className: \"flex-1 py-3 px-4 rounded-lg font-medium transition-colors \".concat(activeTab === \"charts\" ? \"bg-primary-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4 inline ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                \"الرسوم البيانية\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"symptoms\"),\n                            className: \"flex-1 py-3 px-4 rounded-lg font-medium transition-colors \".concat(activeTab === \"symptoms\" ? \"bg-primary-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-4 h-4 inline ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                \"تتبع الأعراض\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"reminders\"),\n                            className: \"flex-1 py-3 px-4 rounded-lg font-medium transition-colors \".concat(activeTab === \"reminders\" ? \"bg-primary-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-4 h-4 inline ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                \"التذكيرات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"analytics\"),\n                            className: \"flex-1 py-3 px-4 rounded-lg font-medium transition-colors \".concat(activeTab === \"analytics\" ? \"bg-primary-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4 inline ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                \"التحليل الذكي\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_16__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: [\n                    activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full opacity-50 -translate-y-12 translate-x-12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 9\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 rounded-xl bg-gradient-to-br from-red-400 to-pink-500 flex items-center justify-center ml-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    \"تقدم الحمل\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-gray-200 rounded-2xl h-6 overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-6 rounded-2xl transition-all duration-1000 ease-out relative overflow-hidden\",\n                                                            style: {\n                                                                width: \"\".concat(results.progressPercentage, \"%\"),\n                                                                background: \"linear-gradient(90deg, #ec4899 0%, #8b5cf6 50%, #06b6d4 100%)\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-white/20 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-lg font-semibold text-gray-700 mt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gray-100 px-3 py-1 rounded-lg\",\n                                                                children: \"البداية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-pink-500 to-purple-500 text-white px-4 py-1 rounded-lg\",\n                                                                children: [\n                                                                    results.progressPercentage.toFixed(1),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gray-100 px-3 py-1 rounded-lg\",\n                                                                children: \"الولادة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 13\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 9\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 7\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-5 h-5 ml-2 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    \"التواريخ المهمة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-green-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"تاريخ الإباضة المتوقع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-700 font-semibold\",\n                                                                children: formatDate(results.ovulationDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-blue-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"تاريخ الولادة المتوقع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-700 font-semibold\",\n                                                                children: formatDate(results.dueDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-purple-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"الأيام المتبقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-purple-700 font-semibold\",\n                                                                children: [\n                                                                    results.daysUntilDue,\n                                                                    \" يوم\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 13\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 9\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-5 h-5 ml-2 text-orange-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    \"معلومات الثلث الحالي\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-orange-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xl font-bold text-orange-800 mb-2\",\n                                                                children: trimesterInfo.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-orange-700 mb-2\",\n                                                                children: trimesterInfo.weeks\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-orange-600\",\n                                                                children: trimesterInfo.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"عمر الجنين:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900 font-semibold mr-2\",\n                                                                children: [\n                                                                    Math.floor(results.fetalAge / 7),\n                                                                    \" أسبوع و \",\n                                                                    results.fetalAge % 7,\n                                                                    \" أيام\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 13\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 9\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 7\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_Baby_BarChart3_Bell_Brain_Calendar_Clock_Globe_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5 ml-2 text-pink-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 11\n                                            }, this),\n                                            \"نصائح الأسبوع \",\n                                            results.currentWeeks\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 9\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-pink-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-pink-800\",\n                                            children: weeklyTip\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 11\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 9\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 7\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"نصائح صحية عامة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 9\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-green-700\",\n                                                        children: \"ما يجب فعله:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-sm space-y-1 text-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• تناول الفيتامينات المخصصة للحمل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• شرب الكثير من الماء\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• ممارسة الرياضة الخفيفة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• الحصول على قسط كافٍ من النوم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• المتابعة الدورية مع الطبيب\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 13\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-red-700\",\n                                                        children: \"ما يجب تجنبه:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-sm space-y-1 text-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• التدخين والكحول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• الكافيين المفرط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• الأطعمة النيئة أو غير المطبوخة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• الأدوية بدون استشارة طبية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• التوتر والضغط النفسي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 13\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 9\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 7\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    activeTab === \"charts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PregnancyCharts__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        results: results\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"symptoms\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SymptomTracker__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        pregnancyWeek: results.currentWeeks\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"reminders\" && lastMenstrualPeriod && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartReminders__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        pregnancyWeek: results.currentWeeks,\n                        lastMenstrualPeriod: lastMenstrualPeriod\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"analytics\" && lastMenstrualPeriod && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HealthAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        results: results,\n                        lastMenstrualPeriod: lastMenstrualPeriod\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, activeTab, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\PregnancyResults.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(PregnancyResults, \"kMNB7K7ehoYrpWwctchpQImrma4=\");\n_c = PregnancyResults;\nvar _c;\n$RefreshReg$(_c, \"PregnancyResults\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PregnancyResults.tsx\n"));

/***/ })

});