'use client';

import { useState, useEffect } from 'react';
import { Brain, TrendingUp, Alert<PERSON>riangle, CheckCircle, Target, Zap } from 'lucide-react';
import { PregnancyResults } from '@/utils/pregnancyCalculations';
import { motion } from 'framer-motion';

interface HealthAnalyticsProps {
  results: PregnancyResults;
  lastMenstrualPeriod: Date;
}

interface HealthInsight {
  id: string;
  type: 'positive' | 'warning' | 'neutral' | 'critical';
  title: string;
  description: string;
  recommendation: string;
  confidence: number;
  category: 'nutrition' | 'exercise' | 'medical' | 'lifestyle';
}

interface RiskAssessment {
  overall: 'low' | 'medium' | 'high';
  factors: {
    age?: 'low' | 'medium' | 'high';
    weight?: 'low' | 'medium' | 'high';
    lifestyle?: 'low' | 'medium' | 'high';
    medical?: 'low' | 'medium' | 'high';
  };
  recommendations: string[];
}

export default function HealthAnalytics({ results, lastMenstrualPeriod }: HealthAnalyticsProps) {
  const [insights, setInsights] = useState<HealthInsight[]>([]);
  const [riskAssessment, setRiskAssessment] = useState<RiskAssessment | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(true);

  useEffect(() => {
    analyzeHealthData();
  }, [results, lastMenstrualPeriod]);

  const analyzeHealthData = async () => {
    setIsAnalyzing(true);
    
    // محاكاة تحليل البيانات
    setTimeout(() => {
      const generatedInsights = generateHealthInsights();
      const assessment = generateRiskAssessment();
      
      setInsights(generatedInsights);
      setRiskAssessment(assessment);
      setIsAnalyzing(false);
    }, 2000);
  };

  const generateHealthInsights = (): HealthInsight[] => {
    const insights: HealthInsight[] = [];
    const { currentWeeks, trimester, progressPercentage } = results;

    // تحليل مرحلة الحمل
    if (trimester === 1) {
      insights.push({
        id: 'trimester1-nutrition',
        type: 'positive',
        title: 'التغذية في الثلث الأول',
        description: 'أنت في مرحلة حرجة لتطور الجنين',
        recommendation: 'ركزي على حمض الفوليك والفيتامينات الأساسية',
        confidence: 0.9,
        category: 'nutrition'
      });

      if (currentWeeks >= 6 && currentWeeks <= 12) {
        insights.push({
          id: 'morning-sickness',
          type: 'neutral',
          title: 'إدارة أعراض الحمل المبكرة',
          description: 'الغثيان والتعب شائعان في هذه المرحلة',
          recommendation: 'تناولي وجبات صغيرة متكررة واشربي الزنجبيل',
          confidence: 0.85,
          category: 'lifestyle'
        });
      }
    } else if (trimester === 2) {
      insights.push({
        id: 'trimester2-exercise',
        type: 'positive',
        title: 'الثلث الثاني - فترة الطاقة',
        description: 'هذه أفضل فترة لممارسة الرياضة والأنشطة',
        recommendation: 'ابدئي برنامج تمارين آمن للحمل',
        confidence: 0.88,
        category: 'exercise'
      });

      if (currentWeeks >= 18 && currentWeeks <= 22) {
        insights.push({
          id: 'anatomy-scan',
          type: 'warning',
          title: 'فحص الموجات فوق الصوتية المفصل',
          description: 'وقت مهم لفحص تطور الجنين',
          recommendation: 'احجزي موعد فحص الموجات فوق الصوتية',
          confidence: 0.95,
          category: 'medical'
        });
      }
    } else {
      insights.push({
        id: 'trimester3-preparation',
        type: 'warning',
        title: 'الاستعداد للولادة',
        description: 'الثلث الأخير - وقت التحضير',
        recommendation: 'ابدئي بحضور دورات الولادة وتحضير حقيبة المستشفى',
        confidence: 0.92,
        category: 'lifestyle'
      });

      if (currentWeeks >= 36) {
        insights.push({
          id: 'full-term-prep',
          type: 'critical',
          title: 'اقتراب موعد الولادة',
          description: 'الجنين مكتمل النمو تقريباً',
          recommendation: 'كوني مستعدة للولادة في أي وقت',
          confidence: 0.98,
          category: 'medical'
        });
      }
    }

    // تحليل التقدم
    if (progressPercentage > 50) {
      insights.push({
        id: 'progress-milestone',
        type: 'positive',
        title: 'تجاوزت منتصف الحمل',
        description: `أكملت ${progressPercentage.toFixed(0)}% من رحلة الحمل`,
        recommendation: 'استمري في المتابعة الطبية المنتظمة',
        confidence: 0.9,
        category: 'medical'
      });
    }

    return insights;
  };

  const generateRiskAssessment = (): RiskAssessment => {
    const { currentWeeks, trimester } = results;
    
    // تقييم المخاطر بناءً على البيانات المتاحة
    let overall: 'low' | 'medium' | 'high' = 'low';
    const recommendations: string[] = [];

    if (trimester === 1) {
      recommendations.push('تناولي حمض الفوليك يومياً');
      recommendations.push('تجنبي الكحول والتدخين');
    } else if (trimester === 2) {
      recommendations.push('مارسي الرياضة الخفيفة');
      recommendations.push('راقبي زيادة الوزن');
    } else {
      recommendations.push('راقبي حركة الجنين يومياً');
      recommendations.push('استعدي للولادة');
      if (currentWeeks > 42) {
        overall = 'medium';
        recommendations.push('استشيري الطبيب فوراً - تأخر الولادة');
      }
    }

    return {
      overall,
      factors: {
        medical: 'low',
        lifestyle: 'low'
      },
      recommendations
    };
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'positive': return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'critical': return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default: return <Target className="w-5 h-5 text-blue-500" />;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'positive': return 'bg-green-50 border-green-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      case 'critical': return 'bg-red-50 border-red-200';
      default: return 'bg-blue-50 border-blue-200';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-green-600 bg-green-100';
    }
  };

  if (isAnalyzing) {
    return (
      <div className="card">
        <div className="text-center py-12">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="inline-block"
          >
            <Brain className="w-12 h-12 text-purple-500" />
          </motion.div>
          <h3 className="text-lg font-semibold mt-4 mb-2">تحليل البيانات الصحية</h3>
          <p className="text-gray-600">الذكاء الاصطناعي يحلل بياناتك...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* تقييم المخاطر العام */}
      {riskAssessment && (
        <div className="card">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <Brain className="w-6 h-6 ml-2 text-purple-500" />
            تقييم المخاطر الذكي
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3">التقييم العام</h4>
              <div className={`inline-flex items-center px-4 py-2 rounded-full ${getRiskColor(riskAssessment.overall)}`}>
                <Zap className="w-4 h-4 ml-1" />
                <span className="font-medium">
                  {riskAssessment.overall === 'low' ? 'مخاطر منخفضة' :
                   riskAssessment.overall === 'medium' ? 'مخاطر متوسطة' : 'مخاطر عالية'}
                </span>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold mb-3">التوصيات الذكية</h4>
              <ul className="space-y-1 text-sm">
                {riskAssessment.recommendations.map((rec, index) => (
                  <li key={index} className="flex items-center">
                    <CheckCircle className="w-3 h-3 text-green-500 ml-1 flex-shrink-0" />
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* الرؤى الصحية */}
      <div className="card">
        <h3 className="text-xl font-bold mb-4 flex items-center">
          <TrendingUp className="w-6 h-6 ml-2 text-blue-500" />
          الرؤى الصحية المخصصة
        </h3>
        
        <div className="grid gap-4">
          {insights.map((insight, index) => (
            <motion.div
              key={insight.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`border rounded-lg p-4 ${getInsightColor(insight.type)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 space-x-reverse">
                  {getInsightIcon(insight.type)}
                  <div className="flex-1">
                    <h4 className="font-semibold">{insight.title}</h4>
                    <p className="text-sm mt-1 opacity-80">{insight.description}</p>
                    <div className="mt-2 p-2 bg-white bg-opacity-50 rounded">
                      <p className="text-sm font-medium">💡 التوصية:</p>
                      <p className="text-sm">{insight.recommendation}</p>
                    </div>
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  دقة {(insight.confidence * 100).toFixed(0)}%
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* ملاحظة مهمة */}
      <div className="card bg-gradient-to-r from-purple-50 to-pink-50">
        <div className="flex items-center space-x-3 space-x-reverse">
          <Brain className="w-8 h-8 text-purple-500" />
          <div>
            <h4 className="font-semibold text-purple-900">تحليل ذكي مخصص</h4>
            <p className="text-sm text-purple-700 mt-1">
              هذا التحليل مبني على الذكاء الاصطناعي وبياناتك الشخصية. 
              استشيري طبيبك دائماً للحصول على نصائح طبية دقيقة.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
