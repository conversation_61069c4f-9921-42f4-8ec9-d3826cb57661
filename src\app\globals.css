@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 30, 41, 59;
  --background-start-rgb: 255, 240, 245;
  --background-end-rgb: 255, 255, 255;
  --gradient-primary: linear-gradient(135deg, #ec4899 0%, #8b5cf6 50%, #06b6d4 100%);
  --gradient-secondary: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 50%, #e0f2fe 100%);
  --shadow-glow: 0 0 30px rgba(236, 72, 153, 0.3);
  --shadow-soft: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

body {
  color: rgb(var(--foreground-rgb));
  background: var(--gradient-secondary);
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  min-height: 100vh;
  overflow-x: hidden;
}

/* خلفية متحركة */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(236, 72, 153, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
  z-index: -1;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(10px) rotate(-1deg); }
}

.ltr {
  direction: ltr;
}

.rtl {
  direction: rtl;
}

@layer components {
  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20;
    box-shadow: var(--shadow-soft);
    transition: all 0.3s ease;
  }

  .card:hover {
    @apply bg-white/90;
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
  }

  .card-gradient {
    background: var(--gradient-primary);
    @apply text-white rounded-2xl p-6;
    box-shadow: var(--shadow-soft);
  }

  .btn-primary {
    @apply text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105;
    background: var(--gradient-primary);
    box-shadow: 0 4px 15px rgba(236, 72, 153, 0.4);
  }

  .btn-primary:hover {
    box-shadow: 0 8px 25px rgba(236, 72, 153, 0.6);
  }

  .btn-secondary {
    @apply bg-white/80 backdrop-blur-sm hover:bg-white text-gray-700 font-medium py-3 px-6 rounded-xl transition-all duration-300 border border-gray-200/50;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-200/50 rounded-xl focus:outline-none transition-all duration-300 bg-white/80 backdrop-blur-sm;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  .input-field:focus {
    @apply border-primary-400;
    box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.1), 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .glass-effect {
    @apply bg-white/20 backdrop-blur-md border border-white/30;
  }

  .gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .floating-animation {
    animation: floating 3s ease-in-out infinite;
  }

  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .slide-up {
    animation: slideUp 0.6s ease-out;
  }
}

@keyframes floating {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(236, 72, 153, 0.4); }
  50% { box-shadow: 0 0 30px rgba(236, 72, 153, 0.8); }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات للسكرول */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #db2777 0%, #7c3aed 50%, #0891b2 100%);
}

/* تحسينات إضافية للتطبيق */
.pregnancy-week-indicator {
  position: relative;
  background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 4px 15px rgba(236, 72, 153, 0.3);
}

.pregnancy-week-indicator::before {
  content: '🤱';
  margin-left: 8px;
}

.fetal-size-comparison {
  background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%);
  border: 2px solid rgba(236, 72, 153, 0.2);
  border-radius: 16px;
  padding: 16px;
  text-align: center;
}

.milestone-badge {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
}

.health-tip-card {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid #3b82f6;
  padding: 16px;
  border-radius: 12px;
  margin: 8px 0;
}

.warning-card {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-left: 4px solid #f59e0b;
  padding: 16px;
  border-radius: 12px;
  margin: 8px 0;
}

.success-card {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-left: 4px solid #10b981;
  padding: 16px;
  border-radius: 12px;
  margin: 8px 0;
}
