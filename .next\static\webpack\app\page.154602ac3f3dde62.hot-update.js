"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SmartReminders.tsx":
/*!*******************************************!*\
  !*** ./src/components/SmartReminders.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartReminders; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,Clock,Pill,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,Clock,Pill,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pill.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,Clock,Pill,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,Clock,Pill,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,Clock,Pill,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,Clock,Pill,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,Clock,Pill,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var date_fns_addDays__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! date-fns/addDays */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var date_fns_differenceInDays__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! date-fns/differenceInDays */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SmartReminders(param) {\n    let { pregnancyWeek, lastMenstrualPeriod } = param;\n    _s();\n    const [reminders, setReminders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCompleted, setShowCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        generateSmartReminders();\n    }, [\n        pregnancyWeek,\n        lastMenstrualPeriod\n    ]);\n    const generateSmartReminders = ()=>{\n        const today = new Date();\n        const generatedReminders = [];\n        // تذكيرات الفحوصات الطبية\n        const medicalCheckups = [\n            {\n                week: 6,\n                title: \"أول زيارة طبية\",\n                description: \"تأكيد الحمل وفحص عام\",\n                priority: \"high\"\n            },\n            {\n                week: 11,\n                title: \"فحص الشفافية القفوية\",\n                description: \"فحص للكشف عن التشوهات الخلقية\",\n                priority: \"high\"\n            },\n            {\n                week: 16,\n                title: \"فحص الدم الثلاثي\",\n                description: \"فحص للكشف عن متلازمة داون\",\n                priority: \"medium\"\n            },\n            {\n                week: 20,\n                title: \"الموجات فوق الصوتية المفصلة\",\n                description: \"فحص شامل لنمو الجنين\",\n                priority: \"high\"\n            },\n            {\n                week: 24,\n                title: \"فحص السكري\",\n                description: \"فحص سكري الحمل\",\n                priority: \"high\"\n            },\n            {\n                week: 28,\n                title: \"فحص الأجسام المضادة\",\n                description: \"فحص عامل الريسوس\",\n                priority: \"medium\"\n            },\n            {\n                week: 32,\n                title: \"مراقبة النمو\",\n                description: \"متابعة نمو الجنين ووضعيته\",\n                priority: \"medium\"\n            },\n            {\n                week: 36,\n                title: \"فحص البكتيريا العقدية\",\n                description: \"فحص البكتيريا العقدية المجموعة ب\",\n                priority: \"high\"\n            }\n        ];\n        medicalCheckups.forEach((checkup, index)=>{\n            const dueDate = (0,date_fns_addDays__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(lastMenstrualPeriod, checkup.week * 7);\n            const daysDiff = (0,date_fns_differenceInDays__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(dueDate, today);\n            if (daysDiff >= -7 && daysDiff <= 14) {\n                generatedReminders.push({\n                    id: \"checkup-\".concat(index),\n                    type: \"checkup\",\n                    title: checkup.title,\n                    description: checkup.description,\n                    dueDate,\n                    priority: checkup.priority,\n                    completed: false,\n                    pregnancyWeek: checkup.week\n                });\n            }\n        });\n        // تذكيرات الأدوية والفيتامينات\n        const medications = [\n            {\n                title: \"حمض الفوليك\",\n                description: \"تناولي 400-800 ميكروجرام يومياً لمنع عيوب الأنبوب العصبي\",\n                frequency: \"daily\"\n            },\n            {\n                title: \"فيتامينات الحمل\",\n                description: \"تناولي الفيتامينات المتعددة المخصصة للحمل\",\n                frequency: \"daily\"\n            },\n            {\n                title: \"الكالسيوم\",\n                description: \"تناولي 1000-1300 مجم يومياً لصحة العظام\",\n                frequency: \"daily\"\n            },\n            {\n                title: \"الحديد\",\n                description: \"تناولي مكملات الحديد لمنع الأنيميا (حسب توصية الطبيب)\",\n                frequency: \"daily\"\n            },\n            {\n                title: \"فيتامين د\",\n                description: \"تناولي 600-800 وحدة دولية يومياً\",\n                frequency: \"daily\"\n            },\n            {\n                title: \"أوميجا 3\",\n                description: \"تناولي مكملات أوميجا 3 لتطور دماغ الجنين\",\n                frequency: \"daily\"\n            }\n        ];\n        medications.forEach((med, index)=>{\n            generatedReminders.push({\n                id: \"medication-\".concat(index),\n                type: \"medication\",\n                title: med.title,\n                description: med.description,\n                dueDate: today,\n                priority: \"medium\",\n                completed: false,\n                pregnancyWeek\n            });\n        });\n        // تذكيرات عامة حسب الأسبوع\n        const weeklyReminders = getWeeklyReminders(pregnancyWeek);\n        weeklyReminders.forEach((reminder, index)=>{\n            generatedReminders.push({\n                id: \"weekly-\".concat(index),\n                type: \"general\",\n                title: reminder.title,\n                description: reminder.description,\n                dueDate: today,\n                priority: reminder.priority,\n                completed: false,\n                pregnancyWeek\n            });\n        });\n        setReminders(generatedReminders);\n    };\n    const getWeeklyReminders = (week)=>{\n        const reminders = [];\n        if (week <= 12) {\n            reminders.push({\n                title: \"تجنبي الكافيين\",\n                description: \"قللي من تناول القهوة والشاي\",\n                priority: \"medium\"\n            }, {\n                title: \"تناولي الزنجبيل\",\n                description: \"يساعد في تخفيف الغثيان\",\n                priority: \"low\"\n            });\n        } else if (week <= 26) {\n            reminders.push({\n                title: \"ابدئي بتمارين كيجل\",\n                description: \"تقوي عضلات الحوض\",\n                priority: \"medium\"\n            }, {\n                title: \"راقبي حركة الجنين\",\n                description: \"ستبدئين بالشعور بالحركة قريباً\",\n                priority: \"low\"\n            });\n        } else {\n            reminders.push({\n                title: \"احضري دورة الولادة\",\n                description: \"تعلمي تقنيات التنفس والاسترخاء\",\n                priority: \"high\"\n            }, {\n                title: \"جهزي حقيبة المستشفى\",\n                description: \"ابدئي بتحضير الأغراض اللازمة\",\n                priority: \"medium\"\n            });\n        }\n        return reminders;\n    };\n    const toggleReminder = (id)=>{\n        setReminders((prev)=>prev.map((reminder)=>reminder.id === id ? {\n                    ...reminder,\n                    completed: !reminder.completed\n                } : reminder));\n    };\n    const getIcon = (type)=>{\n        switch(type){\n            case \"checkup\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 30\n                }, this);\n            case \"medication\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 33\n                }, this);\n            case \"appointment\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 34\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"text-red-600 bg-red-50 border-red-200\";\n            case \"medium\":\n                return \"text-yellow-600 bg-yellow-50 border-yellow-200\";\n            default:\n                return \"text-green-600 bg-green-50 border-green-200\";\n        }\n    };\n    const getDaysUntilDue = (dueDate)=>{\n        const days = (0,date_fns_differenceInDays__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(dueDate, new Date());\n        if (days < 0) return \"متأخر \".concat(Math.abs(days), \" يوم\");\n        if (days === 0) return \"اليوم\";\n        if (days === 1) return \"غداً\";\n        return \"خلال \".concat(days, \" أيام\");\n    };\n    const activeReminders = reminders.filter((r)=>!r.completed);\n    const completedReminders = reminders.filter((r)=>r.completed);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-6 h-6 ml-2 text-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            \"التذكيرات الذكية\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"إظهار المكتملة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompleted(!showCompleted),\n                                className: \"w-10 h-6 rounded-full transition-colors \".concat(showCompleted ? \"bg-purple-600\" : \"bg-gray-300\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 bg-white rounded-full transition-transform \".concat(showCompleted ? \"translate-x-4\" : \"translate-x-1\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                        children: activeReminders.map((reminder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                className: \"border rounded-lg p-4 \".concat(getPriorityColor(reminder.priority)),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1\",\n                                                    children: getIcon(reminder.type)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold\",\n                                                            children: reminder.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm opacity-80 mt-1\",\n                                                            children: reminder.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 space-x-reverse mt-2 text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: getDaysUntilDue(reminder.dueDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                reminder.type === \"checkup\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                                            lineNumber: 213,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"الأسبوع \",\n                                                                                reminder.pregnancyWeek\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                                            lineNumber: 214,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleReminder(reminder.id),\n                                            className: \"text-gray-400 hover:text-green-600 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            }, reminder.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    showCompleted && completedReminders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-600 mb-3\",\n                                children: \"مكتملة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            completedReminders.map((reminder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 rounded-lg p-4 opacity-60\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium line-through\",\n                                                                children: reminder.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: reminder.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleReminder(reminder.id),\n                                                className: \"text-gray-400 hover:text-red-600 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this)\n                                }, reminder.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this),\n                    activeReminders.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_Clock_Pill_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-12 h-12 mx-auto mb-3 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"لا توجد تذكيرات نشطة حالياً\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"ستظهر التذكيرات حسب مرحلة حملك\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\حاسبة\\\\src\\\\components\\\\SmartReminders.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartReminders, \"qRXyxubLfoDIZfTAd5BsCAH5zwI=\");\n_c = SmartReminders;\nvar _c;\n$RefreshReg$(_c, \"SmartReminders\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartReminders.tsx\n"));

/***/ })

});