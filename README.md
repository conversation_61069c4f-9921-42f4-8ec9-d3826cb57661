# حاسبة الحمل المتقدمة مع الذكاء الاصطناعي

تطبيق ويب متقدم لحساب مدة الحمل وتتبع تطور الجنين مع دعم الذكاء الاصطناعي والتقويم الهجري والميلادي.

## 🌟 المميزات الرئيسية

### 📊 حسابات دقيقة
- حساب مدة الحمل الحالية (أسابيع وأيام)
- تحديد تاريخ الإباضة المتوقع
- حساب تاريخ الولادة المتوقع
- تحديد الثلث الحالي للحمل
- حساب عمر الجنين

### 🤖 الذكاء الاصطناعي
- **مساعد ذكي للحمل**: يجيب على أسئلتك ويقدم نصائح مخصصة
- **تحليل صحي ذكي**: تقييم المخاطر وتوصيات مخصصة
- **تتبع الأعراض الذكي**: تحليل الأعراض وتقديم التوصيات
- **التذكيرات الذكية**: تذكيرات مخصصة للفحوصات والأدوية

### 📈 رسوم بيانية تفاعلية
- منحنى نمو الجنين (الطول والوزن)
- توزيع أثلاث الحمل
- جدول الفحوصات المهمة
- شريط تقدم الحمل

### 📅 دعم التقويم المزدوج
- التقويم الميلادي والهجري
- تحويل تلقائي بين التقويمين
- عرض التواريخ بكلا النظامين

### 🎯 مميزات إضافية
- واجهة باللغة العربية مع دعم RTL
- تصميم متجاوب لجميع الأجهزة
- نصائح صحية أسبوعية
- تتبع الأعراض والتذكيرات
- إشعارات تفاعلية

## 🛠️ التقنيات المستخدمة

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Charts**: Recharts
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Notifications**: React Hot Toast
- **Date Handling**: date-fns

## 🚀 التشغيل

### متطلبات النظام
- Node.js 18+ 
- npm أو yarn

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd حاسبة
```

2. **تثبيت المكتبات**
```bash
npm install
```

3. **تشغيل التطبيق**
```bash
npm run dev
```

4. **فتح التطبيق**
افتح المتصفح على `http://localhost:3000`

## 📱 كيفية الاستخدام

### 1. إدخال البيانات
- أدخلي تاريخ أول يوم من آخر دورة شهرية
- اختاري مدة الدورة الشهرية (21-35 يوم)
- اختاري مدة الطمث (اختياري)

### 2. عرض النتائج
- **نظرة عامة**: معلومات أساسية عن الحمل
- **الرسوم البيانية**: رسوم بيانية تفاعلية لنمو الجنين
- **تتبع الأعراض**: تسجيل ومتابعة أعراض الحمل
- **التذكيرات**: تذكيرات ذكية للفحوصات والأدوية
- **التحليل الذكي**: تحليل صحي مخصص بالذكاء الاصطناعي

### 3. المساعد الذكي
- اضغطي على أيقونة الروبوت في الزاوية السفلى
- اطرحي أسئلتك حول الحمل
- احصلي على نصائح مخصصة وتوصيات ذكية

## 🔧 هيكل المشروع

```
src/
├── app/                    # صفحات Next.js
│   ├── globals.css        # الأنماط العامة
│   ├── layout.tsx         # التخطيط الأساسي
│   └── page.tsx           # الصفحة الرئيسية
├── components/            # المكونات
│   ├── AIAssistant.tsx    # المساعد الذكي
│   ├── HealthAnalytics.tsx # التحليل الصحي
│   ├── PregnancyCharts.tsx # الرسوم البيانية
│   ├── PregnancyForm.tsx   # نموذج الإدخال
│   ├── PregnancyResults.tsx # عرض النتائج
│   ├── SmartReminders.tsx  # التذكيرات الذكية
│   └── SymptomTracker.tsx  # تتبع الأعراض
└── utils/                 # المساعدات
    ├── aiAssistant.ts     # محرك الذكاء الاصطناعي
    ├── hijriCalendar.ts   # التقويم الهجري
    └── pregnancyCalculations.ts # حسابات الحمل
```

## 🎨 التخصيص

### الألوان
يمكن تخصيص الألوان من ملف `tailwind.config.js`:

```javascript
colors: {
  primary: {
    50: '#fdf2f8',
    // ... باقي الدرجات
    900: '#831843',
  },
}
```

### الخطوط
الخط المستخدم هو Cairo من Google Fonts، يمكن تغييره من `globals.css`.

## ⚠️ تنويه مهم

هذا التطبيق مخصص للأغراض التعليمية والمعلوماتية فقط. النتائج والتوصيات تقريبية ولا تغني عن:
- الاستشارة الطبية المتخصصة
- المتابعة الدورية مع طبيب النساء والولادة
- الفحوصات الطبية اللازمة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. تنفيذ التغييرات
4. إرسال Pull Request

## 📞 التواصل

لأي استفسارات أو اقتراحات، يرجى التواصل عبر:
- البريد الإلكتروني: [<EMAIL>]
- GitHub Issues: [repository-issues-url]

---

**تم تطويره بعناية لخدمة الأمهات في العالم العربي** ❤️
