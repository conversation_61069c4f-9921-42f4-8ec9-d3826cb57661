'use client';

import { useState } from 'react';
import EnhancedPregnancyForm from '@/components/EnhancedPregnancyForm';
import PregnancyResults from '@/components/PregnancyResults';
import AIAssistant from '@/components/AIAssistant';
import { calculatePregnancy, PregnancyResults as Results } from '@/utils/pregnancyCalculations';
import { AIPregnancyCalculator, type EnhancedPregnancyData } from '@/utils/aiPregnancyCalculations';
import { Baby, Heart, Star, Shield } from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';
import { motion } from 'framer-motion';

export default function Home() {
  const [results, setResults] = useState<Results | null>(null);
  const [enhancedResults, setEnhancedResults] = useState<any>(null);
  const [showResults, setShowResults] = useState(false);
  const [showAI, setShowAI] = useState(false);
  const [lastMenstrualPeriod, setLastMenstrualPeriod] = useState<Date | null>(null);
  const [isUsingAI, setIsUsingAI] = useState(false);

  const handleFormSubmit = (data: EnhancedPregnancyData) => {
    // تحديد ما إذا كان لدينا بيانات إضافية للذكاء الاصطناعي
    const hasEnhancedData = data.age || data.height || data.prePregnancyWeight ||
                           data.medicalConditions?.length || data.previousPregnancies;

    if (hasEnhancedData) {
      // استخدام الذكاء الاصطناعي
      const aiResults = AIPregnancyCalculator.calculateEnhanced(data);
      setEnhancedResults(aiResults);
      setIsUsingAI(true);
      toast.success('تم حساب بيانات الحمل بالذكاء الاصطناعي! 🤖✨');
    } else {
      // استخدام الحسابات العادية
      const basicResults = calculatePregnancy(data);
      setResults(basicResults);
      setIsUsingAI(false);
      toast.success('تم حساب بيانات الحمل بنجاح! 🎉');
    }

    setLastMenstrualPeriod(data.lastMenstrualPeriod);
    setShowResults(true);
  };

  const handleBack = () => {
    setShowResults(false);
    setResults(null);
    setEnhancedResults(null);
    setLastMenstrualPeriod(null);
    setIsUsingAI(false);
  };

  if (showResults && (results || enhancedResults)) {
    const currentResults = isUsingAI ? enhancedResults : results;
    return (
      <main className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 py-8 px-4">
        <PregnancyResults
          results={currentResults}
          onBack={handleBack}
          lastMenstrualPeriod={lastMenstrualPeriod || undefined}
          isEnhanced={isUsingAI}
        />
        <AIAssistant
          pregnancyWeek={currentResults.currentWeeks}
          isOpen={showAI}
          onToggle={() => setShowAI(!showAI)}
        />
        <Toaster position="top-center" />
      </main>
    );
  }

  return (
    <main className="min-h-screen relative overflow-hidden">
      {/* خلفية متدرجة متحركة */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50 via-purple-50 to-cyan-50">
        <div className="absolute top-10 left-10 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div className="absolute top-40 right-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-2000"></div>
        <div className="absolute bottom-10 left-1/2 w-72 h-72 bg-cyan-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-4000"></div>
      </div>

      {/* Hero Section */}
      <div className="relative container mx-auto px-4 py-12">
        <div className="text-center mb-16 slide-up">
          <div className="inline-flex items-center justify-center w-24 h-24 mb-8 floating-animation">
            <div className="w-full h-full rounded-full glass-effect flex items-center justify-center pulse-glow">
              <Baby className="w-12 h-12 text-pink-600" />
            </div>
          </div>
          <h1 className="text-6xl md:text-8xl font-black mb-6 leading-tight">
            <span className="gradient-text">حاسبة الحمل</span>
            <br />
            <span className="text-gray-800">بالذكاء الاصطناعي</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto mb-8 leading-relaxed font-medium">
            احصلي على حسابات دقيقة ومخصصة لحملك مع تحليل شامل للمخاطر وتوقعات نمو الجنين باستخدام
            <span className="gradient-text font-bold"> أحدث تقنيات الذكاء الاصطناعي </span>
            وخوارزميات التعلم الآلي المتقدمة
          </p>

          {/* إحصائيات محسنة */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12">
            <motion.div
              className="text-center p-6 glass-effect rounded-2xl"
              whileHover={{ scale: 1.05 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <div className="text-4xl font-bold gradient-text mb-2">95%</div>
              <div className="text-sm text-gray-600 font-medium">دقة الحسابات</div>
            </motion.div>
            <motion.div
              className="text-center p-6 glass-effect rounded-2xl"
              whileHover={{ scale: 1.05 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="text-4xl font-bold gradient-text mb-2">42</div>
              <div className="text-sm text-gray-600 font-medium">أسبوع تتبع</div>
            </motion.div>
            <motion.div
              className="text-center p-6 glass-effect rounded-2xl"
              whileHover={{ scale: 1.05 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className="text-4xl font-bold gradient-text mb-2">AI</div>
              <div className="text-sm text-gray-600 font-medium">ذكاء اصطناعي</div>
            </motion.div>
            <motion.div
              className="text-center p-6 glass-effect rounded-2xl"
              whileHover={{ scale: 1.05 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <div className="text-4xl font-bold gradient-text mb-2">9</div>
              <div className="text-sm text-gray-600 font-medium">أدوات متقدمة</div>
            </motion.div>
          </div>
          
          {/* Features */}
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto mb-16">
            <div className="card group hover:scale-105 transition-all duration-500">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-red-400 to-pink-500 flex items-center justify-center floating-animation">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-xl text-gray-900 mb-3">حسابات دقيقة بالذكاء الاصطناعي</h3>
                <p className="text-gray-600 leading-relaxed">
                  خوارزميات متقدمة تحلل بياناتك الشخصية لتقديم حسابات مخصصة ودقيقة مع تحليل المخاطر
                </p>
              </div>
            </div>

            <div className="card group hover:scale-105 transition-all duration-500">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-purple-400 to-indigo-500 flex items-center justify-center floating-animation animation-delay-1000">
                  <Star className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-xl text-gray-900 mb-3">تحليل المخاطر الذكي</h3>
                <p className="text-gray-600 leading-relaxed">
                  تقييم شامل للمخاطر مع توصيات مخصصة وتوقعات نمو الجنين ونوع الولادة
                </p>
              </div>
            </div>

            <div className="card group hover:scale-105 transition-all duration-500">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-cyan-400 to-blue-500 flex items-center justify-center floating-animation animation-delay-2000">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-xl text-gray-900 mb-3">أدوات متقدمة شاملة</h3>
                <p className="text-gray-600 leading-relaxed">
                  9 أدوات متخصصة: تتبع الوزن، حركة الجنين، الانقباضات، التغذية الذكية والمزيد
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <EnhancedPregnancyForm onSubmit={handleFormSubmit} />

        {/* Disclaimer */}
        <div className="max-w-3xl mx-auto mt-16">
          <div className="card relative overflow-hidden">
            <div className="absolute top-0 left-0 w-20 h-20 bg-gradient-to-br from-yellow-200 to-orange-200 rounded-full opacity-30 -translate-y-10 -translate-x-10"></div>
            <div className="relative z-10">
              <div className="flex items-start space-x-4 space-x-reverse">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xl">⚠️</span>
                </div>
                <div>
                  <h3 className="font-bold text-xl text-gray-800 mb-3">تنويه مهم</h3>
                  <p className="text-gray-700 leading-relaxed text-lg">
                    هذه الحاسبة تقدم تقديرات تقريبية بناءً على المعلومات المدخلة. النتائج قد تختلف من امرأة لأخرى.
                    يرجى استشارة طبيب النساء والولادة للحصول على متابعة طبية دقيقة ومعلومات موثوقة حول حملك.
                    <span className="font-semibold text-orange-600"> لا تعتبر هذه الحاسبة بديلاً عن الاستشارة الطبية المتخصصة.</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="text-center mt-20 py-12 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-pink-50 via-purple-50 to-cyan-50 rounded-3xl"></div>
          <div className="relative z-10">
            <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-pink-400 to-purple-500 flex items-center justify-center">
              <Heart className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold gradient-text mb-4">
              حاسبة الحمل المتقدمة
            </h3>
            <p className="text-gray-700 text-lg font-medium mb-2">
              © 2024 جميع الحقوق محفوظة
            </p>
            <p className="text-gray-600">
              تم تطويره بعناية وحب لخدمة الأمهات في العالم العربي ❤️
            </p>
          </div>
        </footer>
      </div>

      <AIAssistant
        isOpen={showAI}
        onToggle={() => setShowAI(!showAI)}
      />
      <Toaster position="top-center" />
    </main>
  );
}
