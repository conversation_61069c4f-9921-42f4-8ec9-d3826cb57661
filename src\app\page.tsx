'use client';

import { useState } from 'react';
import PregnancyForm from '@/components/PregnancyForm';
import PregnancyResults from '@/components/PregnancyResults';
import AIAssistant from '@/components/AIAssistant';
import { calculatePregnancy, PregnancyResults as Results } from '@/utils/pregnancyCalculations';
import { Baby, Heart, Star, Shield } from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';

export default function Home() {
  const [results, setResults] = useState<Results | null>(null);
  const [showResults, setShowResults] = useState(false);
  const [showAI, setShowAI] = useState(false);
  const [lastMenstrualPeriod, setLastMenstrualPeriod] = useState<Date | null>(null);

  const handleFormSubmit = (data: {
    lastMenstrualPeriod: Date;
    cycleLength: number;
    menstrualLength?: number;
  }) => {
    const calculatedResults = calculatePregnancy(data);
    setResults(calculatedResults);
    setLastMenstrualPeriod(data.lastMenstrualPeriod);
    setShowResults(true);
    toast.success('تم حساب بيانات الحمل بنجاح! 🎉');
  };

  const handleBack = () => {
    setShowResults(false);
    setResults(null);
    setLastMenstrualPeriod(null);
  };

  if (showResults && results) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 py-8 px-4">
        <PregnancyResults
          results={results}
          onBack={handleBack}
          lastMenstrualPeriod={lastMenstrualPeriod || undefined}
        />
        <AIAssistant
          pregnancyWeek={results.currentWeeks}
          isOpen={showAI}
          onToggle={() => setShowAI(!showAI)}
        />
        <Toaster position="top-center" />
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-primary-100 rounded-full mb-6">
            <Baby className="w-10 h-10 text-primary-600" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            حاسبة الحمل المتقدمة
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            احسبي مدة حملك وتاريخ الولادة المتوقع بدقة مع دعم التقويم الهجري والميلادي
          </p>
          
          {/* Features */}
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <Heart className="w-8 h-8 text-red-500 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">حسابات دقيقة</h3>
              <p className="text-sm text-gray-600">
                حساب مدة الحمل وتاريخ الولادة بناءً على آخر دورة شهرية
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <Star className="w-8 h-8 text-yellow-500 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">ذكاء اصطناعي</h3>
              <p className="text-sm text-gray-600">
                مساعد ذكي وتحليل صحي مخصص لحملك
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <Shield className="w-8 h-8 text-green-500 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">متابعة شاملة</h3>
              <p className="text-sm text-gray-600">
                تتبع الأعراض والتذكيرات الذكية والرسوم البيانية
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <PregnancyForm onSubmit={handleFormSubmit} />

        {/* Disclaimer */}
        <div className="max-w-2xl mx-auto mt-12">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="font-semibold text-yellow-800 mb-2">تنويه مهم</h3>
            <p className="text-sm text-yellow-700 leading-relaxed">
              هذه الحاسبة تقدم تقديرات تقريبية بناءً على المعلومات المدخلة. النتائج قد تختلف من امرأة لأخرى.
              يرجى استشارة طبيب النساء والولادة للحصول على متابعة طبية دقيقة ومعلومات موثوقة حول حملك.
              لا تعتبر هذه الحاسبة بديلاً عن الاستشارة الطبية المتخصصة.
            </p>
          </div>
        </div>

        {/* Footer */}
        <footer className="text-center mt-16 py-8 border-t border-gray-200">
          <p className="text-gray-600">
            © 2024 حاسبة الحمل المتقدمة - جميع الحقوق محفوظة
          </p>
          <p className="text-sm text-gray-500 mt-2">
            تم تطويره بعناية لخدمة الأمهات في العالم العربي
          </p>
        </footer>
      </div>

      <AIAssistant
        isOpen={showAI}
        onToggle={() => setShowAI(!showAI)}
      />
      <Toaster position="top-center" />
    </main>
  );
}
