'use client';

import { useState } from 'react';
import PregnancyForm from '@/components/PregnancyForm';
import PregnancyResults from '@/components/PregnancyResults';
import AIAssistant from '@/components/AIAssistant';
import { calculatePregnancy, PregnancyResults as Results } from '@/utils/pregnancyCalculations';
import { Baby, Heart, Star, Shield } from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';

export default function Home() {
  const [results, setResults] = useState<Results | null>(null);
  const [showResults, setShowResults] = useState(false);
  const [showAI, setShowAI] = useState(false);
  const [lastMenstrualPeriod, setLastMenstrualPeriod] = useState<Date | null>(null);

  const handleFormSubmit = (data: {
    lastMenstrualPeriod: Date;
    cycleLength: number;
    menstrualLength?: number;
  }) => {
    const calculatedResults = calculatePregnancy(data);
    setResults(calculatedResults);
    setLastMenstrualPeriod(data.lastMenstrualPeriod);
    setShowResults(true);
    toast.success('تم حساب بيانات الحمل بنجاح! 🎉');
  };

  const handleBack = () => {
    setShowResults(false);
    setResults(null);
    setLastMenstrualPeriod(null);
  };

  if (showResults && results) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 py-8 px-4">
        <PregnancyResults
          results={results}
          onBack={handleBack}
          lastMenstrualPeriod={lastMenstrualPeriod || undefined}
        />
        <AIAssistant
          pregnancyWeek={results.currentWeeks}
          isOpen={showAI}
          onToggle={() => setShowAI(!showAI)}
        />
        <Toaster position="top-center" />
      </main>
    );
  }

  return (
    <main className="min-h-screen relative overflow-hidden">
      {/* خلفية متدرجة متحركة */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50 via-purple-50 to-cyan-50">
        <div className="absolute top-10 left-10 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div className="absolute top-40 right-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-2000"></div>
        <div className="absolute bottom-10 left-1/2 w-72 h-72 bg-cyan-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-4000"></div>
      </div>

      {/* Hero Section */}
      <div className="relative container mx-auto px-4 py-12">
        <div className="text-center mb-16 slide-up">
          <div className="inline-flex items-center justify-center w-24 h-24 mb-8 floating-animation">
            <div className="w-full h-full rounded-full glass-effect flex items-center justify-center pulse-glow">
              <Baby className="w-12 h-12 text-pink-600" />
            </div>
          </div>
          <h1 className="text-5xl md:text-7xl font-black mb-6 leading-tight">
            <span className="gradient-text">حاسبة الحمل</span>
            <br />
            <span className="text-gray-800">المتقدمة</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-700 max-w-3xl mx-auto mb-8 leading-relaxed font-medium">
            احسبي مدة حملك وتاريخ الولادة المتوقع بدقة مع
            <span className="gradient-text font-bold"> الذكاء الاصطناعي </span>
            ودعم التقويم الهجري والميلادي
          </p>
          
          {/* Features */}
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto mb-16">
            <div className="card group hover:scale-105 transition-all duration-500">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-red-400 to-pink-500 flex items-center justify-center floating-animation">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-xl text-gray-900 mb-3">حسابات دقيقة</h3>
                <p className="text-gray-600 leading-relaxed">
                  حساب مدة الحمل وتاريخ الولادة بناءً على آخر دورة شهرية مع دقة عالية
                </p>
              </div>
            </div>

            <div className="card group hover:scale-105 transition-all duration-500">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-purple-400 to-indigo-500 flex items-center justify-center floating-animation animation-delay-1000">
                  <Star className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-xl text-gray-900 mb-3">ذكاء اصطناعي</h3>
                <p className="text-gray-600 leading-relaxed">
                  مساعد ذكي وتحليل صحي مخصص لحملك مع نصائح شخصية
                </p>
              </div>
            </div>

            <div className="card group hover:scale-105 transition-all duration-500">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-cyan-400 to-blue-500 flex items-center justify-center floating-animation animation-delay-2000">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-xl text-gray-900 mb-3">متابعة شاملة</h3>
                <p className="text-gray-600 leading-relaxed">
                  تتبع الأعراض والتذكيرات الذكية والرسوم البيانية التفاعلية
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <PregnancyForm onSubmit={handleFormSubmit} />

        {/* Disclaimer */}
        <div className="max-w-3xl mx-auto mt-16">
          <div className="card relative overflow-hidden">
            <div className="absolute top-0 left-0 w-20 h-20 bg-gradient-to-br from-yellow-200 to-orange-200 rounded-full opacity-30 -translate-y-10 -translate-x-10"></div>
            <div className="relative z-10">
              <div className="flex items-start space-x-4 space-x-reverse">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xl">⚠️</span>
                </div>
                <div>
                  <h3 className="font-bold text-xl text-gray-800 mb-3">تنويه مهم</h3>
                  <p className="text-gray-700 leading-relaxed text-lg">
                    هذه الحاسبة تقدم تقديرات تقريبية بناءً على المعلومات المدخلة. النتائج قد تختلف من امرأة لأخرى.
                    يرجى استشارة طبيب النساء والولادة للحصول على متابعة طبية دقيقة ومعلومات موثوقة حول حملك.
                    <span className="font-semibold text-orange-600"> لا تعتبر هذه الحاسبة بديلاً عن الاستشارة الطبية المتخصصة.</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="text-center mt-20 py-12 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-pink-50 via-purple-50 to-cyan-50 rounded-3xl"></div>
          <div className="relative z-10">
            <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-pink-400 to-purple-500 flex items-center justify-center">
              <Heart className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold gradient-text mb-4">
              حاسبة الحمل المتقدمة
            </h3>
            <p className="text-gray-700 text-lg font-medium mb-2">
              © 2024 جميع الحقوق محفوظة
            </p>
            <p className="text-gray-600">
              تم تطويره بعناية وحب لخدمة الأمهات في العالم العربي ❤️
            </p>
          </div>
        </footer>
      </div>

      <AIAssistant
        isOpen={showAI}
        onToggle={() => setShowAI(!showAI)}
      />
      <Toaster position="top-center" />
    </main>
  );
}
